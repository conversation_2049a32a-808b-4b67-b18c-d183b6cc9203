#!/usr/bin/env python3
"""
Find installed locations of Node.js and Godot on the system.
"""

import os
import sys
import subprocess
import winreg
import shutil
from pathlib import Path

def find_nodejs_locations():
    """Find all Node.js installations on the system."""
    print("🔍 Searching for Node.js installations...")
    print("-" * 40)
    
    installations = []
    
    # Check if Node.js is in PATH
    node_path = shutil.which('node')
    if node_path:
        try:
            result = subprocess.run([node_path, '--version'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version = result.stdout.strip()
                location = os.path.dirname(node_path)
                installations.append({
                    'location': location,
                    'executable': node_path,
                    'version': version,
                    'in_path': True
                })
                print(f"✅ Found in PATH: {version}")
                print(f"   Location: {location}")
                print(f"   Executable: {node_path}")
        except Exception as e:
            print(f"⚠️  Node.js in PATH but not working: {e}")
    
    # Common installation directories
    search_paths = [
        r"C:\Program Files\nodejs",
        r"C:\Program Files (x86)\nodejs",
        os.path.expanduser(r"~\AppData\Roaming\npm"),
        os.path.expanduser(r"~\AppData\Local\Programs\nodejs"),
        r"C:\nodejs",
        r"D:\nodejs"
    ]
    
    # Check registry for installed programs
    try:
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall") as key:
            i = 0
            while True:
                try:
                    subkey_name = winreg.EnumKey(key, i)
                    with winreg.OpenKey(key, subkey_name) as subkey:
                        try:
                            display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                            if "node" in display_name.lower():
                                try:
                                    install_location = winreg.QueryValueEx(subkey, "InstallLocation")[0]
                                    if install_location and install_location not in search_paths:
                                        search_paths.append(install_location)
                                        print(f"📋 Found in registry: {display_name}")
                                        print(f"   Install location: {install_location}")
                                except FileNotFoundError:
                                    pass
                        except FileNotFoundError:
                            pass
                    i += 1
                except OSError:
                    break
    except Exception as e:
        print(f"⚠️  Could not read registry: {e}")
    
    # Check each search path
    for path in search_paths:
        if os.path.exists(path):
            node_exe = os.path.join(path, "node.exe")
            if os.path.exists(node_exe):
                # Skip if already found in PATH
                if any(inst['location'] == path for inst in installations):
                    continue
                    
                try:
                    result = subprocess.run([node_exe, "--version"], capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        version = result.stdout.strip()
                        installations.append({
                            'location': path,
                            'executable': node_exe,
                            'version': version,
                            'in_path': False
                        })
                        print(f"✅ Found installation: {version}")
                        print(f"   Location: {path}")
                        print(f"   Executable: {node_exe}")
                        print(f"   In PATH: No")
                except Exception:
                    print(f"⚠️  Found Node.js at {path} but not working")
    
    if not installations:
        print("❌ No Node.js installations found")
        print("\n💡 To install Node.js:")
        print("   1. Go to: https://nodejs.org/")
        print("   2. Download the LTS version")
        print("   3. Run the installer")
    
    return installations

def find_godot_locations():
    """Find all Godot installations on the system."""
    print("\n🔍 Searching for Godot installations...")
    print("-" * 40)
    
    installations = []
    
    # Check if Godot is in PATH
    godot_commands = ['godot', 'godot4', 'Godot']
    for cmd in godot_commands:
        godot_path = shutil.which(cmd)
        if godot_path:
            try:
                result = subprocess.run([godot_path, '--version'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    location = os.path.dirname(godot_path)
                    installations.append({
                        'location': location,
                        'executable': godot_path,
                        'version': version,
                        'command': cmd,
                        'in_path': True
                    })
                    print(f"✅ Found in PATH: {version}")
                    print(f"   Command: {cmd}")
                    print(f"   Location: {location}")
                    print(f"   Executable: {godot_path}")
            except Exception as e:
                print(f"⚠️  {cmd} in PATH but not working: {e}")
    
    # Common installation and download locations
    search_paths = [
        r"C:\Godot",
        r"D:\Godot",
        r"C:\Program Files\Godot",
        r"C:\Program Files (x86)\Godot",
        os.path.expanduser(r"~\Downloads"),
        os.path.expanduser(r"~\Desktop"),
        os.path.expanduser(r"~\Documents"),
        os.path.expanduser(r"~\AppData\Local\Programs\Godot"),
        r"C:\Games\Godot",
        r"D:\Games\Godot",
        r"C:\Tools\Godot",
        r"D:\Tools\Godot"
    ]
    
    print(f"\n🔍 Searching in common locations...")
    
    # Search for Godot executables
    for base_path in search_paths:
        if os.path.exists(base_path):
            print(f"   Checking: {base_path}")
            
            # Search recursively but limit depth
            for root, dirs, files in os.walk(base_path):
                # Limit search depth to avoid long searches
                level = root.replace(base_path, '').count(os.sep)
                if level >= 3:
                    dirs[:] = []  # Don't go deeper
                    continue
                
                for file in files:
                    if (file.lower().startswith('godot') and file.lower().endswith('.exe')) or \
                       (file.lower().startswith('godot_v') and file.lower().endswith('.exe')):
                        
                        full_path = os.path.join(root, file)
                        
                        # Skip if already found in PATH
                        if any(inst['executable'] == full_path for inst in installations):
                            continue
                        
                        try:
                            result = subprocess.run([full_path, "--version"], 
                                                  capture_output=True, text=True, timeout=10)
                            if result.returncode == 0:
                                version = result.stdout.strip()
                                installations.append({
                                    'location': root,
                                    'executable': full_path,
                                    'version': version,
                                    'command': file,
                                    'in_path': False
                                })
                                print(f"✅ Found installation: {version}")
                                print(f"   Location: {root}")
                                print(f"   Executable: {full_path}")
                                print(f"   In PATH: No")
                        except Exception:
                            print(f"⚠️  Found {file} at {root} but not working")
    
    if not installations:
        print("❌ No Godot installations found")
        print("\n💡 To install Godot:")
        print("   1. Go to: https://godotengine.org/download")
        print("   2. Download Godot 4.x for Windows")
        print("   3. Extract the ZIP to a folder (e.g., C:\\Godot)")
    
    return installations

def check_npm_locations(node_installations):
    """Check npm locations for each Node.js installation."""
    print("\n🔍 Checking npm locations...")
    print("-" * 40)
    
    npm_locations = []
    
    # Check if npm is in PATH
    npm_path = shutil.which('npm')
    if npm_path:
        try:
            result = subprocess.run([npm_path, '--version'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version = result.stdout.strip()
                location = os.path.dirname(npm_path)
                npm_locations.append({
                    'location': location,
                    'executable': npm_path,
                    'version': version,
                    'in_path': True
                })
                print(f"✅ npm found in PATH: {version}")
                print(f"   Location: {location}")
                print(f"   Executable: {npm_path}")
        except Exception as e:
            print(f"⚠️  npm in PATH but not working: {e}")
    
    # Check npm in each Node.js installation
    for node_inst in node_installations:
        node_dir = node_inst['location']
        npm_candidates = [
            os.path.join(node_dir, 'npm.cmd'),
            os.path.join(node_dir, 'npm.exe'),
            os.path.join(node_dir, 'npm'),
        ]
        
        for npm_candidate in npm_candidates:
            if os.path.exists(npm_candidate):
                # Skip if already found
                if any(npm['executable'] == npm_candidate for npm in npm_locations):
                    continue
                
                try:
                    result = subprocess.run([npm_candidate, '--version'], 
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        version = result.stdout.strip()
                        npm_locations.append({
                            'location': node_dir,
                            'executable': npm_candidate,
                            'version': version,
                            'in_path': False,
                            'node_version': node_inst['version']
                        })
                        print(f"✅ npm found with Node.js {node_inst['version']}: {version}")
                        print(f"   Location: {node_dir}")
                        print(f"   Executable: {npm_candidate}")
                except Exception:
                    pass
    
    if not npm_locations:
        print("❌ No npm installations found")
    
    return npm_locations

def main():
    """Main function to find all installations."""
    print("🔍 Finding Node.js and Godot Installations")
    print("=" * 60)
    
    # Find Node.js
    node_installations = find_nodejs_locations()
    
    # Find npm
    npm_locations = check_npm_locations(node_installations)
    
    # Find Godot
    godot_installations = find_godot_locations()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 INSTALLATION SUMMARY")
    print("=" * 60)
    
    print(f"\n📦 Node.js: {len(node_installations)} installation(s) found")
    for i, inst in enumerate(node_installations, 1):
        print(f"   {i}. {inst['version']} at {inst['location']}")
        print(f"      In PATH: {'Yes' if inst['in_path'] else 'No'}")
    
    print(f"\n📦 npm: {len(npm_locations)} installation(s) found")
    for i, npm in enumerate(npm_locations, 1):
        print(f"   {i}. {npm['version']} at {npm['location']}")
        print(f"      In PATH: {'Yes' if npm['in_path'] else 'No'}")
    
    print(f"\n🎮 Godot: {len(godot_installations)} installation(s) found")
    for i, inst in enumerate(godot_installations, 1):
        print(f"   {i}. {inst['version']} at {inst['location']}")
        print(f"      Command: {inst.get('command', 'N/A')}")
        print(f"      In PATH: {'Yes' if inst['in_path'] else 'No'}")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS")
    print("-" * 30)
    
    if node_installations:
        node_in_path = any(inst['in_path'] for inst in node_installations)
        if not node_in_path:
            print("⚠️  Node.js found but not in PATH")
            print(f"   Add to PATH: {node_installations[0]['location']}")
    else:
        print("❌ Install Node.js from: https://nodejs.org/")
    
    if npm_locations:
        npm_in_path = any(npm['in_path'] for npm in npm_locations)
        if not npm_in_path:
            print("⚠️  npm found but not in PATH")
            print(f"   Add to PATH: {npm_locations[0]['location']}")
    else:
        print("❌ npm not found - should come with Node.js")
    
    if godot_installations:
        godot_in_path = any(inst['in_path'] for inst in godot_installations)
        if not godot_in_path:
            print("⚠️  Godot found but not in PATH")
            print(f"   Add to PATH: {godot_installations[0]['location']}")
    else:
        print("❌ Install Godot from: https://godotengine.org/download")
    
    # Next steps
    if node_installations and godot_installations:
        print(f"\n🚀 NEXT STEPS")
        print("-" * 20)
        print("1. Add missing installations to PATH if needed")
        print("2. Restart your terminal/VS Code")
        print("3. Run: python setup_godot_mcp.py")
    
    return node_installations, npm_locations, godot_installations

if __name__ == "__main__":
    main()
