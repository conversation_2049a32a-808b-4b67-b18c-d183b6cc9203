#!/usr/bin/env python3
"""
Setup and configure <PERSON><PERSON> project settings for the SimRace project.
"""

import os
import subprocess
from pathlib import Path

def update_project_settings():
    """Update the project.godot file with enhanced settings for racing game development."""
    project_file = Path("new-game-project/project.godot")
    
    print("⚙️ UPDATING PROJECT SETTINGS")
    print("=" * 40)
    
    # Enhanced project settings for racing game
    project_content = '''; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="SimRace - AI-Assisted Racing Game"
config/description="Racing game developed with AI assistance using Blender and Godot MCP servers"
run/main_scene="res://BasicPlaneScene.tscn"
config/features=PackedStringArray("4.4", "Forward Plus")
config/icon="res://icon.svg"

[display]

window/size/viewport_width=1920
window/size/viewport_height=1080
window/size/mode=2
window/size/resizable=true
window/stretch/mode="canvas_items"
window/stretch/aspect="expand"

[input]

ui_accept={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":4194309,"physical_keycode":0,"key_label":0,"unicode":0,"echo":false,"script":null)
, Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":4194310,"physical_keycode":0,"key_label":0,"unicode":0,"echo":false,"script":null)
, Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":32,"physical_keycode":0,"key_label":0,"unicode":32,"echo":false,"script":null)
]
}
ui_cancel={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":4194305,"physical_keycode":0,"key_label":0,"unicode":0,"echo":false,"script":null)
]
}
accelerate={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":87,"physical_keycode":0,"key_label":0,"unicode":119,"echo":false,"script":null)
, Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":4194320,"physical_keycode":0,"key_label":0,"unicode":0,"echo":false,"script":null)
]
}
brake={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":83,"physical_keycode":0,"key_label":0,"unicode":115,"echo":false,"script":null)
, Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":4194322,"physical_keycode":0,"key_label":0,"unicode":0,"echo":false,"script":null)
]
}
steer_left={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":65,"physical_keycode":0,"key_label":0,"unicode":97,"echo":false,"script":null)
, Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":4194319,"physical_keycode":0,"key_label":0,"unicode":0,"echo":false,"script":null)
]
}
steer_right={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":68,"physical_keycode":0,"key_label":0,"unicode":100,"echo":false,"script":null)
, Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":4194321,"physical_keycode":0,"key_label":0,"unicode":0,"echo":false,"script":null)
]
}
handbrake={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":32,"physical_keycode":0,"key_label":0,"unicode":32,"echo":false,"script":null)
]
}
reset_car={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":82,"physical_keycode":0,"key_label":0,"unicode":114,"echo":false,"script":null)
]
}
camera_switch={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":67,"physical_keycode":0,"key_label":0,"unicode":99,"echo":false,"script":null)
]
}

[layer_names]

3d_physics/layer_1="Ground"
3d_physics/layer_2="Vehicles"
3d_physics/layer_3="Obstacles"
3d_physics/layer_4="Pickups"
3d_physics/layer_5="Boundaries"

[physics]

3d/default_gravity=9.8
3d/default_linear_damp=0.1
3d/default_angular_damp=0.1

[rendering]

renderer/rendering_method="forward_plus"
textures/canvas_textures/default_texture_filter=2
anti_aliasing/quality/msaa_3d=2
anti_aliasing/quality/screen_space_aa=1
lights_and_shadows/directional_shadow/size=4096
lights_and_shadows/directional_shadow/soft_shadow_filter_quality=3
global_illumination/gi/use_half_resolution=true
environment/defaults/default_clear_color=Color(0.3, 0.5, 0.8, 1)

[debug]

gdscript/warnings/enable=true
gdscript/warnings/unused_variable=true
gdscript/warnings/unused_local_constant=true
gdscript/warnings/unused_private_class_variable=true
gdscript/warnings/unused_parameter=true
gdscript/warnings/unused_signal=true
gdscript/warnings/shadowed_variable=true
gdscript/warnings/unreachable_code=true
gdscript/warnings/standalone_expression=true
gdscript/warnings/narrowing_conversion=true
gdscript/warnings/int_as_enum_without_cast=true
'''

    try:
        with open(project_file, 'w') as f:
            f.write(project_content)
        print("✅ Project settings updated successfully")
        return True
    except Exception as e:
        print(f"❌ Error updating project settings: {e}")
        return False

def open_project_settings_in_godot():
    """Open the project settings in Godot editor."""
    print("\n🎛️ OPENING PROJECT SETTINGS IN GODOT")
    print("=" * 40)
    
    godot_commands = [
        r'C:\Users\<USER>\OneDrive\Desktop\Godot_v4.4.1-stable_win64.exe',
        'godot'
    ]
    
    for godot_cmd in godot_commands:
        try:
            print(f"Attempting to open project settings with: {godot_cmd}")
            
            # Launch Godot with the project and focus on project settings
            result = subprocess.run([
                godot_cmd, '--path', 'new-game-project', '--editor'
            ], capture_output=False, timeout=10)
            
            print("✅ Godot editor launched")
            print("📋 To open Project Settings:")
            print("   1. In Godot Editor: Project → Project Settings")
            print("   2. Or press: Project → Project Settings (Ctrl+Alt+,)")
            return True
            
        except subprocess.TimeoutExpired:
            print("✅ Godot editor launched (timeout expected)")
            print("📋 To open Project Settings:")
            print("   1. In Godot Editor: Project → Project Settings")
            print("   2. Or press: Project → Project Settings (Ctrl+Alt+,)")
            return True
        except FileNotFoundError:
            print(f"❌ Command not found: {godot_cmd}")
            continue
        except Exception as e:
            print(f"❌ Error with {godot_cmd}: {e}")
            continue
    
    print("❌ Could not launch Godot editor")
    return False

def print_project_settings_info():
    """Print information about the project settings."""
    print("\n📋 PROJECT SETTINGS OVERVIEW")
    print("=" * 40)
    
    settings_info = {
        "Application": [
            "Name: SimRace - AI-Assisted Racing Game",
            "Main Scene: BasicPlaneScene.tscn",
            "Engine: Godot 4.4 with Forward Plus rendering"
        ],
        "Display": [
            "Resolution: 1920x1080",
            "Window Mode: Fullscreen",
            "Stretch Mode: Canvas Items"
        ],
        "Input Map": [
            "W/↑: Accelerate",
            "S/↓: Brake", 
            "A/←: Steer Left",
            "D/→: Steer Right",
            "Space: Handbrake",
            "R: Reset Car",
            "C: Camera Switch"
        ],
        "Physics": [
            "Gravity: 9.8 m/s²",
            "Linear Damping: 0.1",
            "Angular Damping: 0.1"
        ],
        "Rendering": [
            "Renderer: Forward Plus",
            "MSAA: 2x",
            "Screen Space AA: Enabled",
            "Shadow Quality: High"
        ],
        "Physics Layers": [
            "Layer 1: Ground",
            "Layer 2: Vehicles", 
            "Layer 3: Obstacles",
            "Layer 4: Pickups",
            "Layer 5: Boundaries"
        ]
    }
    
    for category, items in settings_info.items():
        print(f"\n🔧 {category}:")
        for item in items:
            print(f"   • {item}")

def main():
    """Main function to setup project settings."""
    print("⚙️ GODOT PROJECT SETTINGS SETUP")
    print("=" * 50)
    
    # Update project settings file
    settings_updated = update_project_settings()
    
    # Print project settings info
    print_project_settings_info()
    
    # Instructions for opening in Godot
    print(f"\n🎛️ OPENING PROJECT SETTINGS")
    print("=" * 40)
    print("The Godot editor should already be running.")
    print("To access Project Settings:")
    print()
    print("1. In Godot Editor menu: Project → Project Settings")
    print("2. Or use keyboard shortcut: Ctrl+Alt+,")
    print("3. Or use top menu: Project → Project Settings...")
    print()
    print("📋 Key sections to check:")
    print("   • Application → General (name, main scene)")
    print("   • Display → Window (resolution, mode)")
    print("   • Input Map (racing controls)")
    print("   • Layer Names → 3D Physics (collision layers)")
    print("   • Rendering → Quality (graphics settings)")
    
    # Summary
    print(f"\n" + "=" * 50)
    print("📋 SETUP SUMMARY")
    print("=" * 50)
    
    if settings_updated:
        print("✅ Project settings file updated")
        print("✅ Racing game controls configured")
        print("✅ Physics layers defined")
        print("✅ Rendering optimized for racing")
        print("✅ Ready for racing game development")
        
        print(f"\n🚀 NEXT STEPS:")
        print("1. ✅ Open Project Settings in Godot (Project → Project Settings)")
        print("2. ✅ Review and customize settings as needed")
        print("3. ✅ Test the BasicPlaneScene with new controls")
        print("4. ✅ Start building racing game features")
    else:
        print("❌ Some issues occurred during setup")
        print("💡 Check the errors above and try again")
    
    return settings_updated

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n🎉 Project settings configured! Open Project Settings in Godot to review.")
    else:
        print(f"\n🔧 Setup incomplete - please check errors above")
