@echo off
setlocal enabledelayedexpansion

echo Testing Node.js and Godot setup
echo ================================
echo.

REM Refresh PATH for current session
echo Refreshing PATH...
for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set USER_PATH=%%b
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set SYSTEM_PATH=%%b
set PATH=%SYSTEM_PATH%;%USER_PATH%

echo.
echo Testing Node.js...
where node >nul 2>nul
if %errorlevel% == 0 (
    echo ✓ Node.js found in PATH
    node --version
    for /f "tokens=*" %%i in ('where node') do echo   Location: %%i
    set NODE_OK=true
) else (
    echo ✗ Node.js not found in PATH
    set NODE_OK=false
)

echo.
echo Testing npm...
where npm >nul 2>nul
if %errorlevel% == 0 (
    echo ✓ npm found in PATH
    npm --version
    set NPM_OK=true
) else (
    echo ✗ npm not found in PATH
    set NPM_OK=false
)

echo.
echo Testing Godot...
where godot >nul 2>nul
if %errorlevel% == 0 (
    echo ✓ Godot found in PATH
    godot --version 2>nul
    for /f "tokens=*" %%i in ('where godot') do echo   Location: %%i
    set GODOT_OK=true
    goto godot_found
)

where godot4 >nul 2>nul
if %errorlevel% == 0 (
    echo ✓ Godot4 found in PATH
    godot4 --version 2>nul
    for /f "tokens=*" %%i in ('where godot4') do echo   Location: %%i
    set GODOT_OK=true
    goto godot_found
)

echo ✗ Godot not found in PATH
set GODOT_OK=false

:godot_found

echo.
echo ================================
echo SUMMARY
echo ================================

if "%NODE_OK%"=="true" if "%NPM_OK%"=="true" if "%GODOT_OK%"=="true" (
    echo ✓ All dependencies are working!
    echo.
    echo Ready to run: python setup_godot_mcp.py
    goto end
)

echo Some dependencies need attention:
if "%NODE_OK%"=="false" echo - Node.js needs to be installed or PATH updated
if "%NPM_OK%"=="false" echo - npm needs to be installed or PATH updated  
if "%GODOT_OK%"=="false" echo - Godot needs to be installed or PATH updated

echo.
echo NEXT STEPS:
echo.

if "%NODE_OK%"=="false" (
    echo For Node.js:
    echo 1. Download from: https://nodejs.org/
    echo 2. Install with default settings
    echo 3. Restart terminal
    echo.
)

if "%GODOT_OK%"=="false" (
    echo For Godot:
    echo 1. Download from: https://godotengine.org/download
    echo 2. Extract ZIP to a folder like C:\Godot
    echo 3. Add the folder to your PATH
    echo.
    echo To add Godot to PATH manually:
    echo 1. Press Win + R, type: sysdm.cpl
    echo 2. Click "Environment Variables"
    echo 3. Edit the "Path" variable
    echo 4. Add your Godot folder path
    echo.
)

echo After making changes:
echo 1. Restart your terminal/VS Code
echo 2. Run this script again to verify
echo 3. If successful, run: python setup_godot_mcp.py

:end
echo.
pause
