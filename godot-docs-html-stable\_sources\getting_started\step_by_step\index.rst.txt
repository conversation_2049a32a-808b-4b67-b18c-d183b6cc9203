:allow_comments: False

Step by step
============

This series builds upon the :ref:`Introduction to <PERSON><PERSON>
<toc-learn-introduction>` and will get you started with the editor and the
engine. You will learn more about nodes and scenes, code your first classes with
GDScript, use signals to make nodes communicate with one another, and more.

The following lessons are here to prepare you for :ref:`doc_your_first_2d_game`, a
step-by-step tutorial where you will code a game from scratch. By the end of it,
you will have the necessary foundations to explore more features in other
sections. We also included links to pages that cover a given topic in-depth
where appropriate.

.. toctree::
   :maxdepth: 1
   :name: toc-learn-step_by_step

   nodes_and_scenes
   instancing
   scripting_languages
   scripting_first_script
   scripting_player_input
   signals
