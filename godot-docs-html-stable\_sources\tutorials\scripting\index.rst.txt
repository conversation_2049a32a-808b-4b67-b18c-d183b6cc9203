:allow_comments: False

<PERSON>
=========

This section covers programming languages and core features to code your games
in Godot.

Here, you will find information that is not already covered in more specific
sections. For instance, to learn about inputs, we recommend you to read
:ref:`Inputs <toc-learn-features-inputs>`.

.. rubric:: Programming languages
   :heading-level: 2

The sections below each focus on a given programming language.

.. toctree::
   :maxdepth: 1
   :name: toc-learn-scripting

   gdscript/index
   c_sharp/index
   gdextension/index

Core features
-------------

Some features are specific to the engine and are available in all supported
languages. Whether you code in GDScript, C#, or another language, the pages
below will help you make the most of Godot.

.. To split and organize better, into some related toctrees?

.. toctree::
   :maxdepth: 1
   :name: toc-scripting-core-features

   how_to_read_the_godot_api
   debug/index
   idle_and_physics_processing
   groups
   nodes_and_scene_instances
   overridable_functions
   cross_language_scripting
   creating_script_templates
   evaluating_expressions
   change_scenes_manually
   instancing_with_signals
   pausing_games
   filesystem
   resources
   singletons_autoload
   scene_tree
   scene_unique_nodes
