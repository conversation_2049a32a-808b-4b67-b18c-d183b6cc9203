@echo off
REM Quick start script for Godot MCP server
REM This script starts the Godot MCP server directly

echo 🚀 Starting Godot MCP Server...
echo.

REM Check if the server exists
if not exist "godot-integration\godot-mcp\build\index.js" (
    echo ❌ Godot MCP server not found!
    echo Please run setup_godot_mcp.py first
    echo.
    pause
    exit /b 1
)

REM Start the server
node "godot-integration\godot-mcp\build\index.js"
