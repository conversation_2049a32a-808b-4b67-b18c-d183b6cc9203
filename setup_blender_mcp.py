#!/usr/bin/env python3
"""
Setup script for Blender MCP integration with Augment Code
"""

import os
import sys
import subprocess
import platform
import json
from pathlib import Path

def check_uv_installed():
    """Check if UV package manager is installed."""
    # Try different possible UV locations
    uv_paths = [
        'uv',  # In PATH
        os.path.expanduser('~/.local/bin/uv'),  # Linux/Mac
        os.path.expanduser('~/.local/bin/uv.exe'),  # Windows
        os.path.expanduser(r'~\.local\bin\uv.exe'),  # Windows alternative
    ]

    for uv_path in uv_paths:
        try:
            result = subprocess.run([uv_path, '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ UV is installed: {result.stdout.strip()}")
                print(f"   Location: {uv_path}")
                return True, uv_path
        except FileNotFoundError:
            continue

    print("❌ UV package manager is not installed")
    return False, None

def install_uv():
    """Install UV package manager based on the operating system."""
    system = platform.system().lower()
    
    print(f"Installing UV for {system}...")
    
    if system == "windows":
        print("Run this command in PowerShell:")
        print('powershell -c "irm https://astral.sh/uv/install.ps1 | iex"')
        print("\nThen restart your terminal and run this script again.")
    elif system == "darwin":  # macOS
        try:
            subprocess.run(['brew', 'install', 'uv'], check=True)
            print("✅ UV installed successfully via Homebrew")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Failed to install via Homebrew. Please install manually:")
            print("Visit: https://docs.astral.sh/uv/getting-started/installation/")
    else:  # Linux
        print("Please install UV manually for Linux:")
        print("Visit: https://docs.astral.sh/uv/getting-started/installation/")
    
    return False

def test_blender_mcp(uv_path):
    """Test if blender-mcp can be run via uvx."""
    if not uv_path:
        print("❌ Cannot test Blender MCP without UV")
        return False

    # Get uvx path from uv path
    uvx_path = uv_path.replace('uv.exe', 'uvx.exe').replace('uv', 'uvx')

    try:
        print("Testing blender-mcp installation...")
        result = subprocess.run([uvx_path, 'blender-mcp', '--help'],
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ Blender MCP server is working")
            return True
        else:
            print(f"❌ Blender MCP test failed: {result.stderr}")
    except subprocess.TimeoutExpired:
        print("⚠️  Blender MCP test timed out (this might be normal)")
        return True  # Timeout might be normal for MCP servers
    except FileNotFoundError:
        print(f"❌ uvx command not found at {uvx_path}")
    except Exception as e:
        print(f"❌ Error testing Blender MCP: {e}")

    return False

def check_vscode_settings():
    """Check if VS Code settings are properly configured."""
    settings_path = Path('.vscode/settings.json')
    
    if not settings_path.exists():
        print("❌ VS Code settings.json not found")
        return False
    
    try:
        with open(settings_path, 'r') as f:
            settings = json.load(f)
        
        if 'augment.advanced' in settings and 'mcpServers' in settings['augment.advanced']:
            mcp_servers = settings['augment.advanced']['mcpServers']
            blender_server = next((s for s in mcp_servers if s.get('name') == 'blender'), None)
            
            if blender_server:
                print("✅ Blender MCP server configured in VS Code settings")
                return True
            else:
                print("❌ Blender MCP server not found in VS Code settings")
        else:
            print("❌ MCP servers not configured in VS Code settings")
    except json.JSONDecodeError:
        print("❌ Invalid JSON in VS Code settings")
    except Exception as e:
        print(f"❌ Error reading VS Code settings: {e}")
    
    return False

def check_godot_project():
    """Check if Godot project is properly configured."""
    project_path = Path('new-game-project/project.godot')
    
    if not project_path.exists():
        print("❌ Godot project.godot not found")
        return False
    
    with open(project_path, 'r') as f:
        content = f.read()
    
    if 'import/blender/enabled=true' in content:
        print("✅ Godot project configured for Blender import")
        return True
    else:
        print("❌ Godot project not configured for Blender import")
        return False

def print_next_steps():
    """Print the next steps for the user."""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETE!")
    print("="*60)
    print("\nNext steps:")
    print("1. Download addon.py from: https://github.com/ahujasid/blender-mcp")
    print("2. Install the addon in Blender (Edit > Preferences > Add-ons)")
    print("3. Enable 'Interface: Blender MCP' in Blender")
    print("4. Open VS Code with Augment Code extension")
    print("5. Start Blender and connect to Claude in the BlenderMCP tab")
    print("6. Use Augment Agent to control Blender with natural language!")
    print("\nFor detailed instructions, see: blender-integration/README.md")

def main():
    """Main setup function."""
    print("🚀 Setting up Blender MCP integration with Augment Code")
    print("="*60)

    # Check UV installation
    uv_installed, uv_path = check_uv_installed()
    if not uv_installed:
        install_uv()
        uv_installed, uv_path = check_uv_installed()
        if not uv_installed:
            print("\n❌ Please install UV and run this script again")
            sys.exit(1)

    # Test Blender MCP
    if not test_blender_mcp(uv_path):
        print("\n⚠️  Blender MCP test failed, but this might be normal")
        print("The server will be installed automatically when first used")

    # Check VS Code settings
    check_vscode_settings()

    # Check Godot project
    check_godot_project()

    print_next_steps()

if __name__ == "__main__":
    main()
