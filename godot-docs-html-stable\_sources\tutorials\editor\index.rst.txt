:allow_comments: False
:article_outdated: True

.. _doc_editor_introduction:

Editor introduction
===================

In this section, we cover the Godot editor in general, from its interface to
using it with the command line.

Editor's interface
------------------

The following pages explain how to use the various windows, workspaces, and
docks that make up the Godot editor. We cover some specific editors' interface
in other sections where appropriate. For example, the :ref:`animation editor
<doc_introduction_animation>`.

.. toctree::
   :maxdepth: 1
   :name: toc-editor-interface

   project_manager
   inspector_dock
   project_settings
   default_key_mapping
   customizing_editor

Android editor
--------------

<PERSON><PERSON> offers a native port of the editor running entirely on Android devices.
The Android port can be downloaded from the `Android Downloads page <https://godotengine.org/download/android/>`__.
While we strive for feature parity with the Desktop version of the editor,
the Android port has a certain amount of caveats you should be aware of.

.. toctree::
   :maxdepth: 1
   :name: toc-android-editor

   using_the_android_editor

Web editor
----------

<PERSON><PERSON> offers an HTML5 version of the editor running entirely in your browser. No
download is required to use it, but it has a certain amount of caveats you
should be aware of.

.. toctree::
   :maxdepth: 1
   :name: toc-web-editor

   using_the_web_editor

Advanced features
-----------------

The articles below focus on advanced features useful for experienced developers,
such as calling Godot from the command line and using an external text editor
like Visual Studio Code or Emacs.

.. toctree::
   :maxdepth: 1
   :name: toc-learn-editor

   command_line_tutorial
   external_editor

Managing editor features
------------------------

Godot allows you to remove features from the editor. This may be useful if you're
an educator trying to ease students into the editor slowly, or if you're working on
a project that's only 2D or only 3D and don't want to see what you don't need.

.. toctree::
   :maxdepth: 1
   :name: toc-editor-features

   managing_editor_features
