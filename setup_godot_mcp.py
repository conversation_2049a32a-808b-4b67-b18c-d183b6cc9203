#!/usr/bin/env python3
"""
Godot MCP Integration Setup Script for Augment Code Extension
This script sets up the Godot Model Context Protocol (MCP) server integration.
"""

import os
import sys
import json
import subprocess
import shutil
from pathlib import Path

def check_node_installed():
    """Check if Node.js is installed and get its path."""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js is installed: {result.stdout.strip()}")
            return True, shutil.which('node')
        else:
            print("❌ Node.js is not installed")
            return False, None
    except FileNotFoundError:
        print("❌ Node.js is not installed")
        return False, None

def install_node():
    """Provide instructions for installing Node.js."""
    print("\n📦 Installing Node.js...")
    print("Please install Node.js from: https://nodejs.org/")
    print("After installation, restart this script.")
    return False

def check_godot_installed():
    """Check if Godot is installed and get its path."""
    # Common Godot executable names
    godot_names = ['godot', 'godot.exe', 'Godot.exe', 'godot4', 'godot4.exe']
    
    for name in godot_names:
        godot_path = shutil.which(name)
        if godot_path:
            try:
                result = subprocess.run([godot_path, '--version'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"✅ Godot is installed: {result.stdout.strip()}")
                    return True, godot_path
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
    
    print("❌ Godot is not installed or not in PATH")
    return False, None

def install_godot_mcp_server():
    """Install the Godot MCP server from GitHub."""
    print("\n📦 Installing Godot MCP server...")
    
    # Create godot-integration directory
    godot_dir = Path("godot-integration")
    godot_dir.mkdir(exist_ok=True)
    
    # Clone the repository
    try:
        if (godot_dir / "godot-mcp").exists():
            print("Godot MCP repository already exists, updating...")
            result = subprocess.run(['git', 'pull'], cwd=godot_dir / "godot-mcp", 
                                  capture_output=True, text=True)
        else:
            print("Cloning Godot MCP repository...")
            result = subprocess.run([
                'git', 'clone', 
                'https://github.com/Coding-Solo/godot-mcp.git'
            ], cwd=godot_dir, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Git operation failed: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ Git is not installed. Please install Git first.")
        return False
    
    # Install npm dependencies
    try:
        print("Installing npm dependencies...")
        result = subprocess.run(['npm', 'install'], cwd=godot_dir / "godot-mcp", 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ npm install failed: {result.stderr}")
            return False
            
        print("Building Godot MCP server...")
        result = subprocess.run(['npm', 'run', 'build'], cwd=godot_dir / "godot-mcp", 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ npm build failed: {result.stderr}")
            return False
            
        print("✅ Godot MCP server installed and built successfully")
        return True
        
    except FileNotFoundError:
        print("❌ npm is not installed. Please install Node.js with npm.")
        return False

def test_godot_mcp_server(node_path):
    """Test if the Godot MCP server can be run."""
    if not node_path:
        print("❌ Cannot test Godot MCP server without Node.js")
        return False
    
    godot_mcp_path = Path("godot-integration/godot-mcp/build/index.js")
    if not godot_mcp_path.exists():
        print("❌ Godot MCP server build not found")
        return False
    
    try:
        print("Testing Godot MCP server...")
        # Test with a timeout since MCP servers run continuously
        result = subprocess.run([node_path, str(godot_mcp_path.absolute()), '--help'],
                              capture_output=True, text=True, timeout=10)
        print("✅ Godot MCP server is working")
        return True
    except subprocess.TimeoutExpired:
        print("✅ Godot MCP server started successfully (timeout is expected)")
        return True
    except Exception as e:
        print(f"❌ Error testing Godot MCP server: {e}")
        return False

def check_vscode_settings():
    """Check if VS Code settings are configured for Godot MCP."""
    settings_path = Path(".vscode/settings.json")
    
    if not settings_path.exists():
        print("❌ VS Code settings file not found")
        return False
    
    try:
        with open(settings_path, 'r') as f:
            settings = json.load(f)
        
        if 'augment.advanced' in settings and 'mcpServers' in settings['augment.advanced']:
            mcp_servers = settings['augment.advanced']['mcpServers']
            godot_server = next((s for s in mcp_servers if s.get('name') == 'godot'), None)
            
            if godot_server:
                print("✅ Godot MCP server configured in VS Code settings")
                return True
            else:
                print("❌ Godot MCP server not found in VS Code settings")
        else:
            print("❌ MCP servers not configured in VS Code settings")
    except json.JSONDecodeError:
        print("❌ Invalid JSON in VS Code settings")
    except Exception as e:
        print(f"❌ Error reading VS Code settings: {e}")
    
    return False

def update_vscode_settings(node_path):
    """Update VS Code settings to include Godot MCP server."""
    settings_path = Path(".vscode/settings.json")
    godot_mcp_path = Path("godot-integration/godot-mcp/build/index.js").absolute()
    
    try:
        # Read existing settings
        if settings_path.exists():
            with open(settings_path, 'r') as f:
                settings = json.load(f)
        else:
            settings = {}
        
        # Ensure augment.advanced structure exists
        if 'augment.advanced' not in settings:
            settings['augment.advanced'] = {}
        if 'mcpServers' not in settings['augment.advanced']:
            settings['augment.advanced']['mcpServers'] = []
        
        # Check if godot server already exists
        mcp_servers = settings['augment.advanced']['mcpServers']
        godot_server = next((s for s in mcp_servers if s.get('name') == 'godot'), None)
        
        if godot_server:
            # Update existing server
            godot_server['command'] = node_path
            godot_server['args'] = [str(godot_mcp_path)]
        else:
            # Add new server
            mcp_servers.append({
                "name": "godot",
                "command": node_path,
                "args": [str(godot_mcp_path)]
            })
        
        # Write updated settings
        with open(settings_path, 'w') as f:
            json.dump(settings, f, indent=4)
        
        print("✅ VS Code settings updated with Godot MCP server")
        return True
        
    except Exception as e:
        print(f"❌ Error updating VS Code settings: {e}")
        return False

def check_godot_project():
    """Check if there's a Godot project in the workspace."""
    project_files = list(Path(".").glob("**/project.godot"))
    
    if project_files:
        print(f"✅ Found Godot project(s): {[str(p.parent) for p in project_files]}")
        return True
    else:
        print("⚠️  No Godot project found in workspace")
        print("You can create a new Godot project or open an existing one later")
        return False

def create_wrapper_scripts(node_path):
    """Create wrapper scripts for easy Godot MCP server execution."""
    godot_mcp_path = Path("godot-integration/godot-mcp/build/index.js").absolute()
    
    # Create batch wrapper for Windows
    bat_content = f'''@echo off
REM Godot MCP Wrapper Script
REM This script launches the Godot MCP server using Node.js

REM Set the full path to node
set NODE_PATH="{node_path}"

REM Check if node exists
if not exist %NODE_PATH% (
    echo Error: node.exe not found at %NODE_PATH%
    echo Please check the installation path
    exit /b 1
)

REM Launch godot-mcp with node
%NODE_PATH% "{godot_mcp_path}" %*
'''
    
    with open("godot-mcp-wrapper.bat", "w") as f:
        f.write(bat_content)
    
    # Create PowerShell wrapper
    ps1_content = f'''# Godot MCP Wrapper Script
# This script launches the Godot MCP server using Node.js

$NodePath = "{node_path}"

# Check if node exists
if (-not (Test-Path $NodePath)) {{
    Write-Error "node.exe not found at $NodePath"
    exit 1
}}

# Launch godot-mcp with node, passing through all arguments
& $NodePath "{godot_mcp_path}" $args
'''
    
    with open("godot-mcp-wrapper.ps1", "w") as f:
        f.write(ps1_content)
    
    print("✅ Wrapper scripts created")

def print_next_steps():
    """Print the next steps for the user."""
    print("\n" + "="*60)
    print("🎉 GODOT MCP SETUP COMPLETE!")
    print("="*60)
    print("\nNext steps:")
    print("1. Restart VS Code to load the new MCP server configuration")
    print("2. Open or create a Godot project in your workspace")
    print("3. Use Augment Agent to interact with Godot:")
    print("   - 'Launch Godot editor for my project'")
    print("   - 'Run my Godot project and show any errors'")
    print("   - 'Create a new scene with a Player node'")
    print("   - 'Help me debug this Godot script'")
    print("\nFor detailed instructions, see: godot-integration/README.md")

def main():
    """Main setup function."""
    print("🚀 Setting up Godot MCP integration with Augment Code")
    print("="*60)

    # Check Node.js installation
    node_installed, node_path = check_node_installed()
    if not node_installed:
        install_node()
        sys.exit(1)

    # Check Godot installation (optional but recommended)
    godot_installed, godot_path = check_godot_installed()
    if not godot_installed:
        print("⚠️  Godot not found in PATH, but you can still use the MCP server")
        print("Make sure to install Godot from: https://godotengine.org/")

    # Install Godot MCP server
    if not install_godot_mcp_server():
        print("\n❌ Failed to install Godot MCP server")
        sys.exit(1)

    # Test Godot MCP server
    if not test_godot_mcp_server(node_path):
        print("\n⚠️  Godot MCP server test failed, but this might be normal")
        print("The server should work when properly configured")

    # Update VS Code settings
    if not update_vscode_settings(node_path):
        print("\n❌ Failed to update VS Code settings")
        sys.exit(1)

    # Check Godot project
    check_godot_project()

    # Create wrapper scripts
    create_wrapper_scripts(node_path)

    print_next_steps()

if __name__ == "__main__":
    main()
