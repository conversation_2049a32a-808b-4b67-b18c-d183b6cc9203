# 🔧 Node.js and <PERSON>ot PATH Setup Guide

This guide helps you install and configure Node.js and <PERSON><PERSON> for the Godot MCP integration with Augment Code.

## 🎯 Quick Start

### Option 1: Automated Setup (Recommended)

**PowerShell Script (Best option):**
```powershell
# Right-click PowerShell and "Run as Administrator", then:
powershell -ExecutionPolicy Bypass -File setup_path.ps1
```

**Batch Script:**
```cmd
# Double-click or run:
setup_dependencies.bat
```

**Python Script:**
```bash
python setup_system_dependencies.py
```

### Option 2: Manual Setup

Follow the detailed guide in `MANUAL_PATH_SETUP.md`

## 📋 Prerequisites

Before starting, you need to install the software:

### Install Node.js
1. Go to [nodejs.org](https://nodejs.org/)
2. Download the **LTS version** for Windows
3. Run the installer with **default settings**
4. ✅ This automatically adds Node.js to PATH

### Install Godot
1. Go to [godotengine.org/download](https://godotengine.org/download)
2. Download **Godot 4.x** for Windows
3. Extract the ZIP file to a folder (e.g., `C:\Godot`)
4. ⚠️ This does NOT automatically add to PATH

## 🔍 Verification

After installation, verify everything works:

```bash
# Run the verification script
python verify_setup.py
```

Or test manually:
```cmd
node --version
npm --version
godot --version
```

## 🛠️ Available Scripts

| Script | Purpose | Requirements |
|--------|---------|--------------|
| `setup_path.ps1` | Automated PATH setup | PowerShell, Admin rights recommended |
| `setup_dependencies.bat` | Dependency checker | Command Prompt |
| `setup_system_dependencies.py` | Full automated setup | Python, Admin rights recommended |
| `verify_setup.py` | Verify configuration | Python |
| `MANUAL_PATH_SETUP.md` | Manual instructions | None |

## 🚀 Step-by-Step Process

### Step 1: Install Software

**Node.js (Required):**
- Download from [nodejs.org](https://nodejs.org/)
- Install with default settings
- Should automatically add to PATH

**Godot (Required):**
- Download from [godotengine.org](https://godotengine.org/)
- Extract to a memorable location (e.g., `C:\Godot`)
- Note the extraction path

### Step 2: Configure PATH

**Option A - PowerShell (Recommended):**
```powershell
# Run as Administrator
powershell -ExecutionPolicy Bypass -File setup_path.ps1
```

**Option B - Manual Configuration:**
1. Press `Win + R`, type `sysdm.cpl`
2. Click "Environment Variables"
3. Edit the "Path" variable
4. Add your Godot installation folder

### Step 3: Verify Setup

```bash
python verify_setup.py
```

### Step 4: Run Godot MCP Setup

```bash
python setup_godot_mcp.py
```

## 🔧 Troubleshooting

### Common Issues

**"Command not found" errors:**
- Restart your terminal/VS Code
- Check that software is actually installed
- Verify PATH was modified correctly

**Permission denied:**
- Run PowerShell as Administrator
- Use User PATH instead of System PATH
- Check antivirus software blocking changes

**Node.js issues:**
- Uninstall old versions first
- Use the official installer from nodejs.org
- Ensure both node and npm work

**Godot issues:**
- Remember where you extracted the ZIP
- Point to the folder containing godot.exe
- Try different Godot command names (godot, godot4, Godot)

### Getting Help

1. **Check the verification script:** `python verify_setup.py`
2. **Review manual setup guide:** `MANUAL_PATH_SETUP.md`
3. **Try different automated scripts**
4. **Check Windows PATH tutorials online**

## 📁 File Structure

After setup, your project should have:

```
your-project/
├── setup_path.ps1                 # PowerShell PATH setup
├── setup_dependencies.bat         # Batch dependency checker
├── setup_system_dependencies.py   # Python full setup
├── verify_setup.py               # Verification script
├── MANUAL_PATH_SETUP.md          # Manual instructions
├── PATH_SETUP_README.md          # This file
├── setup_godot_mcp.py            # Godot MCP setup (run after PATH setup)
└── godot-integration/            # Created by Godot MCP setup
    └── godot-mcp/                # Godot MCP server
```

## 🎯 Success Criteria

You'll know everything is working when:

✅ `node --version` shows a version number  
✅ `npm --version` shows a version number  
✅ `godot --version` shows a version number  
✅ `python verify_setup.py` shows all green checkmarks  
✅ `python setup_godot_mcp.py` runs without errors  

## 🔄 Next Steps

Once PATH is configured:

1. **Restart VS Code** to pick up PATH changes
2. **Run Godot MCP setup:** `python setup_godot_mcp.py`
3. **Follow the integration guide:** `GODOT_MCP_SETUP.md`
4. **Start using Augment Agent** with Godot commands

## 💡 Tips

- **Always restart** your terminal/VS Code after PATH changes
- **Use absolute paths** when in doubt
- **Keep track** of where you install software
- **Test frequently** with the verification script
- **Ask for help** if you get stuck

---

**Ready to continue?** Once Node.js and Godot are in your PATH, run:
```bash
python setup_godot_mcp.py
```
