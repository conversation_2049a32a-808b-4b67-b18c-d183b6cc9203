@echo off
REM Godot MCP Wrapper Script
REM This script launches the Godot MCP server using Node.js

REM Set the path to node (will be updated by setup script)
set NODE_PATH="node"

REM Set the path to Godot MCP server
set GODOT_MCP_PATH="godot-integration\godot-mcp\build\index.js"

REM Check if node exists
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: Node.js not found in PATH
    echo Please install Node.js from: https://nodejs.org/
    exit /b 1
)

REM Check if Godot MCP server exists
if not exist %GODOT_MCP_PATH% (
    echo Error: Godot MCP server not found at %GODOT_MCP_PATH%
    echo Please run setup_godot_mcp.py first
    exit /b 1
)

REM Launch godot-mcp with node
%NODE_PATH% %GODOT_MCP_PATH% %*
