#!/usr/bin/env python3
"""
Comprehensive diagnostic and fix script for Godot car setup issues.
Based on documentation review and common troubleshooting steps.
"""

import os
import subprocess
import sys
from pathlib import Path

def diagnose_godot_installation():
    """Diagnose Godot installation issues."""
    print("🔍 DIAGNOSING GODOT INSTALLATION")
    print("=" * 50)
    
    godot_paths = [
        r"C:\Users\<USER>\OneDrive\Desktop\Godot_v4.4.1-stable_win64.exe",
        r"C:\Users\<USER>\OneDrive\Desktop\Godot_v4.4-stable_win64.exe",
        r"C:\Users\<USER>\OneDrive\Desktop\Godot_v4.3-stable_win64.exe",
        "godot"
    ]
    
    working_godot = None
    
    for path in godot_paths:
        print(f"\n🧪 Testing: {path}")
        
        if path != "godot" and not os.path.exists(path):
            print(f"   ❌ File not found")
            continue
        
        try:
            # Test basic version command
            result = subprocess.run([path, "--version"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"   ✅ Working: {version}")
                working_godot = path
                break
            else:
                print(f"   ❌ Failed with return code: {result.returncode}")
                if result.stderr:
                    print(f"   Error: {result.stderr.strip()}")
                    
        except subprocess.TimeoutExpired:
            print(f"   ⚠️  Timeout (may still work)")
            working_godot = path
            break
        except FileNotFoundError:
            print(f"   ❌ Command not found")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return working_godot

def check_project_structure():
    """Check if the project structure is correct."""
    print(f"\n📁 CHECKING PROJECT STRUCTURE")
    print("=" * 50)
    
    project_dir = Path("new-game-project")
    required_files = {
        "project.godot": "Project configuration",
        "RacingScene.tscn": "Main racing scene",
        "Car.tscn": "Car scene",
        "CarSimple.gd": "Car script",
        "RacingSceneSimple.gd": "Racing scene script"
    }
    
    missing_files = []
    
    for file, description in required_files.items():
        file_path = project_dir / file
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"   ✅ {file}: {description} ({size} bytes)")
        else:
            print(f"   ❌ {file}: {description} (MISSING)")
            missing_files.append(file)
    
    return len(missing_files) == 0

def test_project_validation(godot_path):
    """Test if Godot can validate the project."""
    print(f"\n🧪 TESTING PROJECT VALIDATION")
    print("=" * 50)
    
    if not godot_path:
        print("❌ No working Godot found")
        return False
    
    try:
        print(f"Using Godot: {godot_path}")
        
        # Test project validation
        cmd = [godot_path, "--headless", "--path", "new-game-project", "--quit"]
        print(f"Running: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        print(f"Return code: {result.returncode}")
        
        if result.stdout:
            print(f"STDOUT:\n{result.stdout}")
        
        if result.stderr:
            print(f"STDERR:\n{result.stderr}")
        
        # Check for specific errors
        if "SCRIPT ERROR" in result.stderr:
            print("❌ Script errors found")
            return False
        elif "ERROR" in result.stderr and "Failed loading" in result.stderr:
            print("❌ Scene loading errors found")
            return False
        else:
            print("✅ Project validation passed")
            return True
            
    except subprocess.TimeoutExpired:
        print("⚠️  Validation timeout (may be normal)")
        return True
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False

def fix_project_settings():
    """Fix common project settings issues."""
    print(f"\n🔧 FIXING PROJECT SETTINGS")
    print("=" * 50)
    
    project_file = Path("new-game-project/project.godot")
    
    if not project_file.exists():
        print("❌ project.godot not found")
        return False
    
    # Read current settings
    with open(project_file, 'r') as f:
        content = f.read()
    
    # Check for missing physics section
    if "[physics]" not in content:
        print("🔧 Adding missing [physics] section")
        physics_section = """
[physics]

3d/default_gravity=9.8
3d/default_linear_damp=0.1
3d/default_angular_damp=0.1
"""
        content += physics_section
    
    # Check for missing rendering settings
    if "renderer/rendering_method" not in content:
        print("🔧 Adding missing rendering method")
        content = content.replace("[rendering]", """[rendering]

renderer/rendering_method="forward_plus\"""")
    
    # Write back the fixed content
    with open(project_file, 'w') as f:
        f.write(content)
    
    print("✅ Project settings updated")
    return True

def create_simple_test_scene():
    """Create a very simple test scene to verify Godot is working."""
    print(f"\n🎮 CREATING SIMPLE TEST SCENE")
    print("=" * 50)
    
    # Create a minimal test scene
    test_scene_content = '''[gd_scene load_steps=2 format=3]

[sub_resource type="BoxMesh" id="BoxMesh_1"]

[node name="TestScene" type="Node3D"]

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
mesh = SubResource("BoxMesh_1")

[node name="Camera3D" type="Camera3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 0.866025, 0.5, 0, -0.5, 0.866025, 0, 2, 5)
'''
    
    test_scene_path = Path("new-game-project/TestScene.tscn")
    
    with open(test_scene_path, 'w') as f:
        f.write(test_scene_content)
    
    print(f"✅ Created simple test scene: {test_scene_path}")
    return True

def test_simple_scene(godot_path):
    """Test the simple scene to verify basic Godot functionality."""
    print(f"\n🧪 TESTING SIMPLE SCENE")
    print("=" * 50)
    
    if not godot_path:
        print("❌ No working Godot found")
        return False
    
    try:
        # Test loading the simple scene
        cmd = [godot_path, "--headless", "--path", "new-game-project", 
               "--main-scene", "res://TestScene.tscn", "--quit"]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=20)
        
        print(f"Return code: {result.returncode}")
        
        if result.stderr and "ERROR" in result.stderr:
            print(f"❌ Errors in simple scene test:")
            print(result.stderr)
            return False
        else:
            print("✅ Simple scene test passed")
            return True
            
    except Exception as e:
        print(f"❌ Simple scene test error: {e}")
        return False

def provide_recommendations(godot_working, project_valid, simple_scene_works):
    """Provide recommendations based on test results."""
    print(f"\n💡 RECOMMENDATIONS")
    print("=" * 50)
    
    if not godot_working:
        print("❌ GODOT INSTALLATION ISSUE:")
        print("   1. Download Godot 4.4.1 from godotengine.org")
        print("   2. Place it on your Desktop")
        print("   3. Verify with: godot --version")
        return
    
    if not project_valid:
        print("❌ PROJECT STRUCTURE ISSUE:")
        print("   1. Check that all required files exist")
        print("   2. Verify file permissions")
        print("   3. Try recreating missing files")
        return
    
    if not simple_scene_works:
        print("❌ BASIC GODOT FUNCTIONALITY ISSUE:")
        print("   1. Try running Godot editor manually")
        print("   2. Check graphics drivers")
        print("   3. Try compatibility rendering mode")
        return
    
    print("✅ BASIC FUNCTIONALITY WORKING")
    print("Next steps:")
    print("   1. Try opening Godot editor manually")
    print("   2. Load the TestScene.tscn first")
    print("   3. Then try the RacingScene.tscn")
    print("   4. Check for script errors in the editor")

def main():
    """Main diagnostic function."""
    print("🔍 COMPREHENSIVE GODOT DIAGNOSTIC")
    print("=" * 60)
    print("This will diagnose and attempt to fix Godot issues")
    print()
    
    # Step 1: Check Godot installation
    godot_path = diagnose_godot_installation()
    godot_working = godot_path is not None
    
    # Step 2: Check project structure
    project_valid = check_project_structure()
    
    # Step 3: Fix project settings
    if project_valid:
        fix_project_settings()
    
    # Step 4: Create simple test scene
    create_simple_test_scene()
    
    # Step 5: Test simple scene
    simple_scene_works = test_simple_scene(godot_path)
    
    # Step 6: Test full project validation
    if simple_scene_works:
        full_project_works = test_project_validation(godot_path)
    else:
        full_project_works = False
    
    # Summary
    print(f"\n" + "=" * 60)
    print("📋 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    results = {
        "Godot Installation": godot_working,
        "Project Structure": project_valid,
        "Simple Scene Test": simple_scene_works,
        "Full Project Test": full_project_works
    }
    
    for test, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test}: {status}")
    
    # Provide recommendations
    provide_recommendations(godot_working, project_valid, simple_scene_works)
    
    if all(results.values()):
        print(f"\n🎉 ALL TESTS PASSED!")
        print("Your Godot setup should be working correctly.")
        print("Try launching Godot editor manually:")
        print(f'   "{godot_path}" --path new-game-project --editor')
    else:
        print(f"\n⚠️  ISSUES FOUND - See recommendations above")
    
    return all(results.values())

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ Diagnostic complete - setup should be working!")
    else:
        print(f"\n🔧 Issues found - follow recommendations to fix")
