#!/usr/bin/env python3
"""
Add Node.js and Godot 4.4.1 to Windows environment PATH variable.
"""

import os
import sys
import subprocess
import winreg
from pathlib import Path

def get_current_path(scope="User"):
    """Get current PATH environment variable."""
    try:
        if scope == "System":
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                               r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment") as key:
                return winreg.QueryValueEx(key, "PATH")[0]
        else:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment") as key:
                try:
                    return winreg.QueryValueEx(key, "PATH")[0]
                except FileNotFoundError:
                    return ""
    except Exception as e:
        print(f"Error reading PATH: {e}")
        return ""

def add_to_path(new_path, scope="User"):
    """Add a path to the environment PATH variable."""
    try:
        current_path = get_current_path(scope)
        
        # Check if path already exists (case-insensitive)
        path_parts = [p.strip() for p in current_path.split(';') if p.strip()]
        if new_path.lower() in [p.lower() for p in path_parts]:
            print(f"✅ {new_path} already in {scope} PATH")
            return True
        
        # Add new path
        new_full_path = current_path + ";" + new_path if current_path else new_path
        
        if scope == "System":
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                               r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment",
                               0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "PATH", 0, winreg.REG_EXPAND_SZ, new_full_path)
        else:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment", 0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "PATH", 0, winreg.REG_EXPAND_SZ, new_full_path)
        
        print(f"✅ Added {new_path} to {scope} PATH")
        return True
        
    except Exception as e:
        print(f"❌ Failed to add {new_path} to {scope} PATH: {e}")
        return False

def check_admin_rights():
    """Check if running with administrator privileges."""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def verify_paths_exist():
    """Verify that the paths we want to add actually exist."""
    paths_to_check = {
        "Node.js": r"C:\Program Files\nodejs",
        "Godot Desktop": r"C:\Users\<USER>\OneDrive\Desktop"
    }
    
    print("🔍 Verifying paths exist...")
    
    verified_paths = {}
    
    for name, path in paths_to_check.items():
        if os.path.exists(path):
            print(f"   ✅ {name}: {path}")
            verified_paths[name] = path
        else:
            print(f"   ❌ {name}: {path} (not found)")
    
    return verified_paths

def verify_executables():
    """Verify that the executables exist in the paths."""
    print("\n🔍 Verifying executables...")
    
    executables = {
        "node.exe": r"C:\Program Files\nodejs\node.exe",
        "npm.cmd": r"C:\Program Files\nodejs\npm.cmd",
        "Godot 4.4.1": r"C:\Users\<USER>\OneDrive\Desktop\Godot_v4.4.1-stable_win64.exe"
    }
    
    verified_exes = {}
    
    for name, exe_path in executables.items():
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"   ✅ {name}: {exe_path} ({size_mb:.1f} MB)")
            verified_exes[name] = exe_path
        else:
            print(f"   ❌ {name}: {exe_path} (not found)")
    
    return verified_exes

def test_commands_after_path_update():
    """Test if commands work after PATH update."""
    print("\n🧪 Testing commands...")
    
    # Update current session PATH
    user_path = get_current_path("User")
    system_path = get_current_path("System")
    os.environ["PATH"] = system_path + ";" + user_path
    
    commands_to_test = [
        ("node", ["node", "--version"]),
        ("npm", ["npm", "--version"]),
        ("Godot 4.4.1", [r"C:\Users\<USER>\OneDrive\Desktop\Godot_v4.4.1-stable_win64.exe", "--version"])
    ]
    
    results = {}
    
    for name, cmd in commands_to_test:
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"   ✅ {name}: {version}")
                results[name] = True
            else:
                print(f"   ❌ {name}: Command failed")
                results[name] = False
        except subprocess.TimeoutExpired:
            print(f"   ⚠️  {name}: Timeout (may still work)")
            results[name] = True  # Timeout often means it's working
        except FileNotFoundError:
            print(f"   ❌ {name}: Not found in PATH")
            results[name] = False
        except Exception as e:
            print(f"   ❌ {name}: Error - {e}")
            results[name] = False
    
    return results

def refresh_environment():
    """Refresh environment variables."""
    try:
        # Broadcast environment change
        import ctypes
        from ctypes import wintypes
        
        HWND_BROADCAST = 0xFFFF
        WM_SETTINGCHANGE = 0x001A
        SMTO_ABORTIFHUNG = 0x0002
        
        ctypes.windll.user32.SendMessageTimeoutW(
            HWND_BROADCAST, WM_SETTINGCHANGE, 0, "Environment",
            SMTO_ABORTIFHUNG, 5000, ctypes.byref(wintypes.DWORD())
        )
        print("✅ Environment variables refreshed")
    except Exception:
        print("⚠️  Please restart your terminal/VS Code to apply PATH changes")

def create_godot_batch_file():
    """Create a batch file to make 'godot' command work with latest version."""
    desktop_path = r"C:\Users\<USER>\OneDrive\Desktop"
    godot_exe = os.path.join(desktop_path, "Godot_v4.4.1-stable_win64.exe")
    batch_path = os.path.join(desktop_path, "godot.bat")
    
    batch_content = f'''@echo off
REM Godot 4.4.1 Launcher
REM This batch file allows you to use 'godot' command for Godot 4.4.1
"{godot_exe}" %*
'''
    
    try:
        with open(batch_path, 'w') as f:
            f.write(batch_content)
        print(f"✅ Created godot.bat: {batch_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to create godot.bat: {e}")
        return False

def main():
    """Main function to add Node.js and Godot to PATH."""
    print("🔧 ADDING NODE.JS AND GODOT 4.4.1 TO ENVIRONMENT PATH")
    print("=" * 70)
    
    # Check admin rights
    is_admin = check_admin_rights()
    scope = "System" if is_admin else "User"
    
    if is_admin:
        print("✅ Running as Administrator - will modify System PATH")
    else:
        print("⚠️  Running as regular user - will modify User PATH only")
        print("   (Run as Administrator to modify System PATH)")
    
    print(f"📍 Target scope: {scope} PATH")
    print()
    
    # Verify paths exist
    verified_paths = verify_paths_exist()
    
    if not verified_paths:
        print("❌ No valid paths found to add")
        return False
    
    # Verify executables
    verified_exes = verify_executables()
    
    # Add paths to environment
    print(f"\n📝 Adding paths to {scope} PATH...")
    
    success_count = 0
    total_paths = len(verified_paths)
    
    for name, path in verified_paths.items():
        print(f"\n   Adding {name}: {path}")
        if add_to_path(path, scope):
            success_count += 1
        else:
            print(f"   ❌ Failed to add {name}")
    
    # Create Godot batch file
    print(f"\n🔗 Creating Godot command alias...")
    create_godot_batch_file()
    
    # Refresh environment
    print(f"\n🔄 Refreshing environment...")
    refresh_environment()
    
    # Test commands
    test_results = test_commands_after_path_update()
    
    # Summary
    print(f"\n" + "=" * 70)
    print("📋 ENVIRONMENT PATH SETUP SUMMARY")
    print("=" * 70)
    
    print(f"\n✅ Paths added to {scope} PATH:")
    for name, path in verified_paths.items():
        print(f"   📁 {name}: {path}")
    
    print(f"\n🔧 Executables verified:")
    for name, exe_path in verified_exes.items():
        print(f"   📄 {name}: ✅")
    
    print(f"\n🧪 Command tests:")
    for name, result in test_results.items():
        status = "✅ Working" if result else "❌ Failed"
        print(f"   💻 {name}: {status}")
    
    # Next steps
    print(f"\n🚀 NEXT STEPS:")
    
    all_working = all(test_results.values())
    
    if all_working:
        print("🎉 All commands are working!")
        print("1. ✅ Environment setup complete")
        print("2. ✅ Ready to use Node.js and Godot")
        print("3. ✅ Continue with MCP integration")
    else:
        print("⚠️  Some commands need terminal restart:")
        print("1. Restart your terminal/VS Code")
        print("2. Test commands:")
        print("   - node --version")
        print("   - npm --version") 
        print("   - godot --version")
        print("3. If working, continue with MCP setup")
    
    print(f"\n💡 USAGE TIPS:")
    print(f"   - Use 'node' and 'npm' for Node.js")
    print(f"   - Use 'godot' for Godot 4.4.1 (latest)")
    print(f"   - Or use full path: 'Godot_v4.4.1-stable_win64.exe'")
    print(f"   - All Godot versions available on Desktop")
    
    return success_count == total_paths

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n🎉 Environment PATH setup completed successfully!")
    else:
        print(f"\n⚠️  Some issues occurred - check output above")
