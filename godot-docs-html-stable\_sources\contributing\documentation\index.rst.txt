:allow_comments: False

.. _doc_contributing_writing_documentation:

Writing documentation
=====================

We always need help to improve the documentation, be it the class reference or
the manual. Below, you can find our content and writing guidelines and
concrete guides to make changes to the documentation.

Be sure to also check the :ref:`workflow guidelines <doc_contributing_workflow>`,
especially if you're new to using Git or GitHub.

Guidelines
----------

Here are the principles and guidelines we strive to follow to write accessible
documentation.

.. toctree::
   :maxdepth: 1
   :name: toc-contributing-writing-guidelines

   content_guidelines
   docs_writing_guidelines
   docs_image_guidelines

Writing the manual
------------------

These articles explain how to contribute to this very documentation, and
how to build the online version locally for testing.

.. toctree::
   :maxdepth: 1
   :name: toc-contributing-documentation

   contributing_to_the_documentation
   building_the_manual

Class reference guides
----------------------

The pages below focus on the class reference.

As the reference is included in the Godot editor, its source files are part of
the `godot repository <https://github.com/godotengine/godot>`_. We use XML files
to write it, so the process to contribute to the class reference differs from
writing the online manual.

.. toctree::
   :maxdepth: 1
   :name: toc-contributing-class-reference

   updating_the_class_reference
   class_reference_primer

Translating the documentation
-----------------------------

The community is always working hard on making Godot and its documentation
available to more people. Localizing the documentation is a colossal and ongoing
effort you can be part of.

.. toctree::
   :maxdepth: 1
   :name: toc-contributing-localization

   editor_and_docs_localization
