:allow_comments: False

Migrating to a new version
==========================

Godot :ref:`loosely follows <doc_release_policy>` a semantic versioning system,
where compatibility is assumed between minor and patch releases, while major
releases can break it. As such, it is generally not recommended to move projects
between major versions during their development, especially if you've been
working on them for a significant amount of time.

Still, new features, usability improvements, or paradigm shifts in engine's
internals may incentivize you to upgrade. Below is a list of articles that should
assist you when upgrading your project between versions. Each article would try
its best to document every important difference and provide you with a migration
path.

.. toctree::
   :maxdepth: 1
   :name: toc-migrating

   upgrading_to_godot_4
   upgrading_to_godot_4.1
   upgrading_to_godot_4.2
   upgrading_to_godot_4.3
