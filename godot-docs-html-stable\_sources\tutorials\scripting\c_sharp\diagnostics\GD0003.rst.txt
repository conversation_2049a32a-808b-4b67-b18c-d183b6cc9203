GD0003: Found multiple classes with the same name in the same script file
=========================================================================

====================================  ======================================
                                      Value
====================================  ======================================
**Rule ID**                           GD0003
**Category**                          Usage
**Fix is breaking or non-breaking**   Non-breaking
**Enabled by default**                Yes
====================================  ======================================

Cause
-----

A script file contains multiple types that derives from ``GodotObject`` with
a name that matches the script file. Only one type in the script file should
match the file name.

Rule description
----------------

Godot requires scripts to have a unique path so every type must be defined on its
own file and the type name must match the file name.

.. code-block:: csharp

    public partial class MyNode : Node { }

    namespace DifferentNamespace
    {
        // Invalid because there's already a type with the name MyNode in this file.
        public partial class MyNode : Node { }
    }

    // Invalid because there's already a type with the name MyNode in this file.
    public partial class MyNode<T> : Node { }

How to fix violations
---------------------

To fix a violation of this rule, move each type declaration to a different file.

When to suppress warnings
-------------------------

Do not suppress a warning from this rule. Types that derive from ``GodotObject``
must have a unique path otherwise the engine can't load the script by path,
resulting in unexpected runtime errors.
