# SimRace - Blender MCP Integration Project

## Project Structure

```
simrace/
├── .cursor/
│   └── mcp.json                    # Cursor MCP configuration (legacy)
├── .vscode/
│   └── settings.json               # VS Code settings with Augment MCP config
├── blender-integration/
│   ├── README.md                   # Detailed setup and usage guide
│   ├── example_workflows.md        # Racing game workflow examples
│   └── assets/                     # Blender source files
│       ├── vehicles/               # Car models and variants
│       ├── tracks/                 # Track pieces and layouts
│       ├── environment/            # Environmental objects
│       └── materials/              # Shared materials and textures
├── new-game-project/               # Main Godot project
│   ├── project.godot              # Godot project configuration
│   ├── assets/                    # Imported game assets
│   ├── scenes/                    # Godot scene files
│   └── scripts/                   # GDScript files
├── godot-docs-html-stable/        # Local Godot documentation
├── setup_blender_mcp.py           # Automated setup script
└── PROJECT_OVERVIEW.md            # This file
```

## What This Integration Provides

### 🎨 AI-Assisted 3D Modeling
- Use natural language to create racing game assets in Blender
- Generate vehicles, tracks, and environmental objects
- Automatic material and texture application
- Integration with Poly Haven for realistic assets

### 🔄 Seamless Asset Pipeline
- Direct .blend file import into Godot
- Automatic format conversion and optimization
- Consistent naming and organization
- Performance-optimized asset generation

### 🏎️ Racing Game Specific Features
- Pre-configured workflows for vehicle creation
- Track and environment generation
- Racing-specific materials and shaders
- Performance optimization for real-time rendering

### 🤖 Augment Code Integration
- MCP server configuration for VS Code
- Natural language control of Blender
- Context-aware asset generation
- Automated workflow execution

## Quick Start

### Blender Integration
1. **Install Prerequisites**:
   ```bash
   python setup_blender_mcp.py
   ```

2. **Configure Augment Code**:
   - Open VS Code with Augment extension
   - MCP server is pre-configured in `.vscode/settings.json`

3. **Install Blender Addon**:
   - Download `addon.py` from [Blender MCP repo](https://github.com/ahujasid/blender-mcp)
   - Install in Blender: Edit > Preferences > Add-ons

4. **Start Creating**:
   - Open Blender and connect to Claude
   - Use Augment Agent with natural language prompts
   - Assets automatically sync to Godot project

### Godot Integration
1. **Run Setup Script**:
   ```bash
   python setup_godot_mcp.py
   ```

2. **Prerequisites Check**:
   - Node.js installed and in PATH
   - Git available for cloning repositories
   - Godot Engine installed (optional but recommended)

3. **Start Developing**:
   - Use Augment Agent to interact with Godot
   - Create scenes, nodes, and game mechanics
   - Debug and analyze projects with AI assistance

## Key Features

### Blender MCP Server
- **Command**: `uvx blender-mcp`
- **Capabilities**: 3D modeling, material creation, scene setup
- **Integration**: Poly Haven assets, Hyper3D AI models
- **Export**: Godot-compatible formats

### Godot MCP Server
- **Command**: `node godot-integration/godot-mcp/build/index.js`
- **Capabilities**: Project management, scene creation, node manipulation
- **Integration**: Direct Godot editor control, debug output capture
- **Features**: Scene management, asset loading, project analysis

### Godot Project Configuration
- **Version**: Godot 4.4 with Forward Plus rendering
- **Blender Import**: Enabled and configured
- **Asset Pipeline**: Optimized for racing game development
- **Performance**: LOD and optimization ready

### Development Workflow
1. **Design**: Use AI to describe desired assets and game mechanics
2. **Create**: Blender MCP generates 3D models, Godot MCP creates scenes
3. **Import**: Assets automatically available in Godot
4. **Develop**: Use Godot MCP for scripting and game logic
5. **Integrate**: Combine assets and code in racing game scenes
6. **Debug**: AI-assisted debugging and optimization
7. **Optimize**: Performance tuning and LOD creation

## Example Use Cases

### Vehicle Creation
```
"Create a Formula 1 race car with aerodynamic wings, 
open cockpit, and realistic materials. Use red and 
white color scheme with carbon fiber details."
```

### Track Design
```
"Design a racing circuit with elevation changes, 
banking turns, and proper safety barriers. Include 
pit lane and spectator areas."
```

### Environment Building
```
"Create a racing environment with realistic lighting,
sky dome, and surrounding landscape. Add grandstands
and track-side objects."
```

### Game Development
```
"Create a player controller script for a racing car
with realistic physics and handling."
```

```
"Set up a lap timing system with checkpoints and
race position tracking."
```

```
"Create a UI system for the racing game with speedometer,
lap times, and position display."
```

## Technical Details

### MCP Configuration
- **Blender Server**: Blender MCP via uvx
- **Godot Server**: Godot MCP via Node.js
- **Protocol**: Model Context Protocol
- **Integration**: Augment Code extension
- **Communication**: Socket-based real-time control

### Asset Formats
- **Source**: .blend files in blender-integration/
- **Import**: Automatic glTF conversion
- **Target**: Godot-compatible scenes and resources
- **Optimization**: LOD, compression, performance tuning

### Performance Considerations
- **Polygon Limits**: Optimized for real-time rendering
- **Texture Sizes**: Power-of-2, compressed formats
- **LOD System**: Multiple detail levels
- **Culling**: Occlusion and frustum culling ready

## Documentation

- **Blender Setup**: `blender-integration/README.md`
- **Godot Setup**: `godot-integration/README.md`
- **Workflows**: `blender-integration/example_workflows.md`
- **Godot Docs**: `godot-docs-html-stable/`
- **MCP Docs**: [Augment MCP Documentation](https://docs.augmentcode.com/setup-augment/mcp)

## Support and Resources

### Community
- [Blender MCP Discord](https://discord.gg/z5apgR8TFU)
- [Augment Code Discord](https://augmentcode.com/discord)
- [Godot Community](https://godotengine.org/community)

### Documentation
- [Blender MCP Repository](https://github.com/ahujasid/blender-mcp)
- [Godot MCP Repository](https://github.com/Coding-Solo/godot-mcp)
- [Augment Code Docs](https://docs.augmentcode.com/)
- [Godot Documentation](https://docs.godotengine.org/)

### Assets and Resources
- [Poly Haven](https://polyhaven.com/) - Free 3D assets and HDRIs
- [Hyper3D](https://hyper3d.ai/) - AI-generated 3D models
- [Godot Asset Library](https://godotengine.org/asset-library/asset)

## License

This project integrates multiple open-source tools:
- Blender MCP: MIT License
- Godot MCP: MIT License
- Godot Engine: MIT License
- Augment Code: Commercial license required

## Contributing

1. Fork the repository
2. Create feature branches for new workflows
3. Test with racing game scenarios
4. Submit pull requests with documentation
5. Share successful prompts and techniques

---

**Ready to start creating?**
- For 3D modeling: Run `python setup_blender_mcp.py`
- For game development: Run `python setup_godot_mcp.py`
- Follow the respective setup guides!
