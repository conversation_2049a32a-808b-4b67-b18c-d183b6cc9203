#!/usr/bin/env python3
"""
Quick verification script to check if <PERSON>de.js and <PERSON><PERSON> are properly configured.
"""

import subprocess
import shutil
import sys

def check_command(command, name):
    """Check if a command is available and working."""
    try:
        result = subprocess.run([command, '--version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            path = shutil.which(command)
            print(f"✅ {name}: {version}")
            print(f"   Location: {path}")
            return True
        else:
            print(f"❌ {name}: Command failed")
            return False
    except subprocess.TimeoutExpired:
        print(f"⚠️  {name}: Command timed out")
        return False
    except FileNotFoundError:
        print(f"❌ {name}: Command not found")
        return False
    except Exception as e:
        print(f"❌ {name}: Error - {e}")
        return False

def main():
    """Main verification function."""
    print("🔍 Verifying System Dependencies")
    print("=" * 40)
    
    # Check Node.js
    print("\n1. Node.js:")
    node_ok = check_command('node', 'Node.js')
    
    print("\n2. npm:")
    npm_ok = check_command('npm', 'npm')
    
    # Check Godot (try multiple common names)
    print("\n3. Godot:")
    godot_commands = ['godot', 'godot4', 'Godot']
    godot_ok = False
    
    for cmd in godot_commands:
        if check_command(cmd, f'Godot ({cmd})'):
            godot_ok = True
            break
    
    if not godot_ok:
        print("❌ Godot: Not found with any common command name")
    
    # Summary
    print("\n" + "=" * 40)
    print("📋 VERIFICATION SUMMARY")
    print("=" * 40)
    
    if node_ok and npm_ok and godot_ok:
        print("🎉 All dependencies are properly configured!")
        print("\n✅ Ready to run: python setup_godot_mcp.py")
    else:
        print("⚠️  Some dependencies need attention:")
        
        if not node_ok or not npm_ok:
            print("- Node.js/npm needs to be installed or added to PATH")
        if not godot_ok:
            print("- Godot needs to be installed or added to PATH")
        
        print("\n📋 Next steps:")
        print("1. Install missing software")
        print("2. Add to PATH using one of these methods:")
        print("   - Run: powershell -ExecutionPolicy Bypass -File setup_path.ps1")
        print("   - Run: setup_dependencies.bat")
        print("   - Follow: MANUAL_PATH_SETUP.md")
        print("3. Restart your terminal/VS Code")
        print("4. Run this verification script again")

if __name__ == "__main__":
    main()
