[gd_scene load_steps=4 format=3 uid="uid://bvnxm8qfqkxqy"]

[ext_resource type="Texture2D" uid="uid://bvnxm8qfqkxqy" path="res://icon.svg" id="1_icon"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_1"]
albedo_texture = ExtResource("1_icon")
uv1_scale = Vector3(10, 10, 1)

[sub_resource type="PlaneMesh" id="PlaneMesh_1"]
size = Vector2(20, 20)
subdivide_width = 10
subdivide_depth = 10

[node name="World" type="Node3D"]

[node name="Ground" type="MeshInstance3D" parent="."]
mesh = SubResource("PlaneMesh_1")
surface_material_override/0 = SubResource("StandardMaterial3D_1")

[node name="Camera3D" type="Camera3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 0.707107, 0.707107, 0, -0.707107, 0.707107, 0, 5, 10)

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 5, 0)
light_energy = 1.0
shadow_enabled = true
