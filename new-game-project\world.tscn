[gd_scene load_steps=4 format=3 uid="uid://dqrmlyi7sbs75"]

[ext_resource type="Texture2D" uid="uid://co1oyw25yeb48" path="res://icon.svg" id="1_icon"]

[sub_resource type="PlaneMesh" id="PlaneMesh_1"]
size = Vector2(50, 50)
subdivide_width = 20
subdivide_depth = 20

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_1"]
albedo_texture = ExtResource("1_icon")
roughness = 0.8
uv1_scale = Vector3(10, 10, 1)

[node name="World" type="Node3D"]

[node name="Ground" type="MeshInstance3D" parent="."]
mesh = SubResource("PlaneMesh_1")
surface_material_override/0 = SubResource("StandardMaterial3D_1")

[node name="Camera3D" type="Camera3D" parent="."]
transform = Transform3D(0.866025, -0.25, 0.433013, 0, 0.866025, 0.5, -0.5, -0.433013, 0.75, 0, 8, 15)

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 10, 0)
light_energy = 1.2
shadow_enabled = true
