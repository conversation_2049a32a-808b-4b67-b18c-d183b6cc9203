.. _doc_xr_next_steps:

Where to go from here
=====================

Now that we have the basics covered there are several options to look at for your XR game dev journey:

* You can take a look at the :ref:`Advanced topics <openxr-advanced-topics>` section.
* You can look at a number of `XR demos here <https://github.com/godotengine/godot-demo-projects/tree/master/xr>`_.
* You can find 3rd party tutorials on our :ref:`Tutorials and resources <doc_community_tutorials>` page.

XR Toolkits
-----------

There are various XR toolkits available that implement more complex XR logic ready for you to use.
We have a :Ref:`small introduction to Godot XR Tools <godot-xr-tools>` that you can look at,
a toolkit developed by core contributors of Godot.

There are more toolkits available for Godot:

* `Godot XR handtracking toolkit <https://github.com/RevolNoom/godot_xr_handtracking>`_ (GDScript)
* `Godot XR Kit <https://github.com/pat<PERSON><PERSON><PERSON><PERSON>/godot-xr-kit>`_ (GDScript)
* `Godot XR Tools <https://github.com/godotvr/godot-xr-tools>`_ (GDScript)
* `NXR <https://github.com/stumpynub/NXR>`_ (C#)
