#!/usr/bin/env python3
"""
System Dependencies Setup Script
This script helps install and configure Node.js and <PERSON><PERSON> for the MCP integration.
"""

import os
import sys
import subprocess
import winreg
import shutil
from pathlib import Path
import requests
import zipfile
import json

def check_admin_rights():
    """Check if the script is running with administrator privileges."""
    try:
        return os.getuid() == 0
    except AttributeError:
        # Windows
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

def get_system_path():
    """Get the current system PATH."""
    try:
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                           r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment") as key:
            path_value, _ = winreg.QueryValueEx(key, "PATH")
            return path_value
    except Exception as e:
        print(f"Error reading system PATH: {e}")
        return ""

def add_to_system_path(new_path):
    """Add a directory to the system PATH."""
    if not check_admin_rights():
        print("⚠️  Administrator rights required to modify system PATH")
        print("Please run this script as administrator or add paths manually")
        return False
    
    try:
        current_path = get_system_path()
        if new_path.lower() in current_path.lower():
            print(f"✅ {new_path} is already in system PATH")
            return True
        
        new_system_path = current_path + ";" + new_path
        
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                           r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment",
                           0, winreg.KEY_SET_VALUE) as key:
            winreg.SetValueEx(key, "PATH", 0, winreg.REG_EXPAND_SZ, new_system_path)
        
        print(f"✅ Added {new_path} to system PATH")
        return True
    except Exception as e:
        print(f"❌ Error adding to system PATH: {e}")
        return False

def check_nodejs_installed():
    """Check if Node.js is installed and get its path."""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            node_path = shutil.which('node')
            print(f"✅ Node.js is installed: {result.stdout.strip()}")
            print(f"   Location: {node_path}")
            return True, node_path
        else:
            print("❌ Node.js is not working properly")
            return False, None
    except FileNotFoundError:
        print("❌ Node.js is not installed or not in PATH")
        return False, None

def download_nodejs():
    """Download and install Node.js."""
    print("\n📦 Setting up Node.js installation...")
    
    # Common Node.js installation paths
    possible_paths = [
        r"C:\Program Files\nodejs",
        r"C:\Program Files (x86)\nodejs",
        os.path.expanduser(r"~\AppData\Roaming\npm"),
        os.path.expanduser(r"~\AppData\Local\Programs\nodejs")
    ]
    
    # Check if Node.js is already installed but not in PATH
    for path in possible_paths:
        node_exe = Path(path) / "node.exe"
        if node_exe.exists():
            print(f"✅ Found Node.js at: {path}")
            if add_to_system_path(path):
                return True, path
    
    print("Node.js not found in common locations.")
    print("\n📋 Please install Node.js manually:")
    print("1. Go to: https://nodejs.org/")
    print("2. Download the LTS version for Windows")
    print("3. Run the installer with default settings")
    print("4. Restart your terminal/VS Code")
    print("5. Run this script again")
    
    return False, None

def check_godot_installed():
    """Check if Godot is installed and get its path."""
    # Common Godot executable names
    godot_names = ['godot', 'godot.exe', 'Godot.exe', 'godot4', 'godot4.exe']
    
    for name in godot_names:
        godot_path = shutil.which(name)
        if godot_path:
            try:
                result = subprocess.run([godot_path, '--version'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"✅ Godot is installed: {result.stdout.strip()}")
                    print(f"   Location: {godot_path}")
                    return True, godot_path
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
    
    print("❌ Godot is not installed or not in PATH")
    return False, None

def find_godot_installation():
    """Find existing Godot installation."""
    print("\n🔍 Searching for Godot installation...")
    
    # Common Godot installation paths
    possible_paths = [
        r"C:\Program Files\Godot",
        r"C:\Program Files (x86)\Godot",
        os.path.expanduser(r"~\Downloads"),
        os.path.expanduser(r"~\Desktop"),
        os.path.expanduser(r"~\AppData\Local\Programs\Godot"),
        r"C:\Godot",
        r"D:\Godot"
    ]
    
    godot_executables = []
    
    for base_path in possible_paths:
        if os.path.exists(base_path):
            for root, dirs, files in os.walk(base_path):
                for file in files:
                    if file.lower() in ['godot.exe', 'godot4.exe', 'godot_v4.exe']:
                        full_path = os.path.join(root, file)
                        try:
                            result = subprocess.run([full_path, '--version'], 
                                                  capture_output=True, text=True, timeout=5)
                            if result.returncode == 0:
                                godot_executables.append((full_path, result.stdout.strip()))
                        except:
                            continue
    
    if godot_executables:
        print(f"✅ Found Godot installation(s):")
        for i, (path, version) in enumerate(godot_executables):
            print(f"   {i+1}. {path} ({version})")
        
        # Use the first one found
        godot_path = godot_executables[0][0]
        godot_dir = os.path.dirname(godot_path)
        
        if add_to_system_path(godot_dir):
            return True, godot_path
    
    return False, None

def download_godot():
    """Provide instructions for downloading Godot."""
    print("\n📦 Setting up Godot installation...")
    
    # First try to find existing installation
    found, path = find_godot_installation()
    if found:
        return True, path
    
    print("Godot not found in common locations.")
    print("\n📋 Please install Godot manually:")
    print("1. Go to: https://godotengine.org/download")
    print("2. Download Godot 4.x for Windows")
    print("3. Extract the ZIP file to a folder (e.g., C:\\Godot)")
    print("4. Note the path to the Godot executable")
    print("5. Run this script again")
    
    return False, None

def update_environment():
    """Refresh environment variables."""
    print("\n🔄 Refreshing environment variables...")
    try:
        # Broadcast WM_SETTINGCHANGE to update environment
        import ctypes
        from ctypes import wintypes
        
        HWND_BROADCAST = 0xFFFF
        WM_SETTINGCHANGE = 0x001A
        SMTO_ABORTIFHUNG = 0x0002
        
        ctypes.windll.user32.SendMessageTimeoutW(
            HWND_BROADCAST, WM_SETTINGCHANGE, 0, "Environment",
            SMTO_ABORTIFHUNG, 5000, ctypes.byref(wintypes.DWORD())
        )
        print("✅ Environment variables refreshed")
    except Exception as e:
        print(f"⚠️  Could not refresh environment: {e}")
        print("Please restart your terminal/VS Code to apply changes")

def create_manual_setup_guide():
    """Create a manual setup guide for PATH configuration."""
    guide_content = """# Manual PATH Setup Guide

If the automated script cannot modify your system PATH, follow these steps:

## Adding Node.js to PATH

1. **Find Node.js installation path**:
   - Usually: `C:\\Program Files\\nodejs`
   - Or: `C:\\Program Files (x86)\\nodejs`

2. **Add to PATH**:
   - Press `Win + R`, type `sysdm.cpl`, press Enter
   - Click "Environment Variables"
   - Under "System Variables", find and select "Path"
   - Click "Edit" → "New"
   - Add the Node.js path (e.g., `C:\\Program Files\\nodejs`)
   - Click "OK" to save

## Adding Godot to PATH

1. **Find Godot executable path**:
   - Note where you extracted/installed Godot
   - Example: `C:\\Godot\\` (folder containing godot.exe)

2. **Add to PATH**:
   - Follow the same steps as Node.js
   - Add the folder containing godot.exe
   - Click "OK" to save

## Verify Installation

Open a new Command Prompt and test:
```cmd
node --version
npm --version
godot --version
```

## Alternative: User PATH

If you don't have admin rights, add to "User Variables" instead of "System Variables".
"""
    
    with open("MANUAL_PATH_SETUP.md", "w") as f:
        f.write(guide_content)
    
    print("📝 Created MANUAL_PATH_SETUP.md for manual configuration")

def main():
    """Main setup function."""
    print("🔧 System Dependencies Setup for Godot MCP Integration")
    print("="*60)
    
    admin_rights = check_admin_rights()
    if not admin_rights:
        print("⚠️  Running without administrator privileges")
        print("Some operations may require manual configuration")
    
    # Check Node.js
    print("\n1. Checking Node.js installation...")
    node_installed, node_path = check_nodejs_installed()
    
    if not node_installed:
        success, path = download_nodejs()
        if not success:
            print("❌ Node.js setup incomplete")
    
    # Check Godot
    print("\n2. Checking Godot installation...")
    godot_installed, godot_path = check_godot_installed()
    
    if not godot_installed:
        success, path = download_godot()
        if not success:
            print("❌ Godot setup incomplete")
    
    # Update environment
    if admin_rights:
        update_environment()
    
    # Create manual guide
    create_manual_setup_guide()
    
    print("\n" + "="*60)
    print("🎉 DEPENDENCY SETUP COMPLETE!")
    print("="*60)
    
    # Final verification
    print("\n🔍 Final verification:")
    node_ok, _ = check_nodejs_installed()
    godot_ok, _ = check_godot_installed()
    
    if node_ok and godot_ok:
        print("✅ All dependencies are properly configured!")
        print("\nNext steps:")
        print("1. Restart your terminal/VS Code")
        print("2. Run: python setup_godot_mcp.py")
    else:
        print("⚠️  Some dependencies need manual configuration")
        print("Please check MANUAL_PATH_SETUP.md for instructions")
        
        if not node_ok:
            print("- Node.js needs to be installed/configured")
        if not godot_ok:
            print("- Godot needs to be installed/configured")

if __name__ == "__main__":
    main()
