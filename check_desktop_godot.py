#!/usr/bin/env python3
"""
Check the Desktop directory thoroughly for all Godot installations and exe files.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_desktop_directory():
    """Check the Desktop directory for all Godot-related files."""
    desktop_path = r"C:\Users\<USER>\OneDrive\Desktop"
    
    print(f"🔍 CHECKING DESKTOP DIRECTORY FOR GODOT")
    print("=" * 60)
    print(f"📁 Directory: {desktop_path}")
    print()
    
    if not os.path.exists(desktop_path):
        print(f"❌ Directory does not exist: {desktop_path}")
        return []
    
    print("📋 ALL FILES IN DESKTOP DIRECTORY:")
    print("-" * 40)
    
    all_files = []
    godot_files = []
    exe_files = []
    
    try:
        # List all files in the directory
        for item in os.listdir(desktop_path):
            item_path = os.path.join(desktop_path, item)
            
            if os.path.isfile(item_path):
                file_size = os.path.getsize(item_path)
                size_mb = file_size / (1024 * 1024)
                
                all_files.append({
                    'name': item,
                    'path': item_path,
                    'size_mb': size_mb,
                    'size_bytes': file_size
                })
                
                print(f"📄 {item} ({size_mb:.1f} MB)")
                
                # Check if it's an exe file
                if item.lower().endswith('.exe'):
                    exe_files.append({
                        'name': item,
                        'path': item_path,
                        'size_mb': size_mb
                    })
                
                # Check if it's Godot-related
                if 'godot' in item.lower():
                    godot_files.append({
                        'name': item,
                        'path': item_path,
                        'size_mb': size_mb
                    })
            
            elif os.path.isdir(item_path):
                print(f"📁 {item}/ (directory)")
    
    except PermissionError:
        print("❌ Permission denied accessing the directory")
        return []
    except Exception as e:
        print(f"❌ Error reading directory: {e}")
        return []
    
    print(f"\n📊 SUMMARY:")
    print(f"   Total files: {len(all_files)}")
    print(f"   Executable files (.exe): {len(exe_files)}")
    print(f"   Godot-related files: {len(godot_files)}")
    
    # Show all exe files
    if exe_files:
        print(f"\n🔧 ALL EXECUTABLE FILES (.exe):")
        print("-" * 40)
        for exe in exe_files:
            print(f"   📄 {exe['name']} ({exe['size_mb']:.1f} MB)")
    
    # Show all Godot-related files
    if godot_files:
        print(f"\n🎮 ALL GODOT-RELATED FILES:")
        print("-" * 40)
        for godot in godot_files:
            print(f"   📄 {godot['name']} ({godot['size_mb']:.1f} MB)")
    
    return godot_files, exe_files, all_files

def test_godot_executables(godot_files, exe_files):
    """Test all potential Godot executables."""
    print(f"\n🧪 TESTING GODOT EXECUTABLES:")
    print("-" * 40)
    
    working_godot = []
    
    # Test obvious Godot files first
    test_files = godot_files.copy()
    
    # Also test any large exe files that might be Godot
    for exe in exe_files:
        if exe['size_mb'] > 50:  # Godot is usually 50MB+
            if exe not in test_files:
                test_files.append(exe)
                print(f"   🔍 Testing large exe: {exe['name']} ({exe['size_mb']:.1f} MB)")
    
    for file_info in test_files:
        exe_path = file_info['path']
        exe_name = file_info['name']
        
        print(f"\n   Testing: {exe_name}")
        
        try:
            # Test with --version
            result = subprocess.run([exe_path, "--version"], 
                                  capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0 and result.stdout.strip():
                version = result.stdout.strip()
                print(f"   ✅ WORKING GODOT: {version}")
                
                working_godot.append({
                    'name': exe_name,
                    'path': exe_path,
                    'version': version,
                    'size_mb': file_info['size_mb']
                })
            else:
                print(f"   ❌ Not Godot or not working")
                
        except subprocess.TimeoutExpired:
            print(f"   ⚠️  Timeout (might be Godot but hanging)")
            # Try --help as backup
            try:
                result = subprocess.run([exe_path, "--help"], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"   ✅ LIKELY GODOT: Responds to --help")
                    working_godot.append({
                        'name': exe_name,
                        'path': exe_path,
                        'version': 'Unknown (timeout on --version)',
                        'size_mb': file_info['size_mb']
                    })
            except:
                print(f"   ❌ No response to --help either")
                
        except FileNotFoundError:
            print(f"   ❌ File not found or not executable")
        except Exception as e:
            print(f"   ❌ Error testing: {e}")
    
    return working_godot

def extract_version_details(version_string):
    """Extract detailed version information."""
    import re
    
    details = {
        'version_string': version_string,
        'major_version': 'Unknown',
        'full_version': 'Unknown',
        'build_type': 'Unknown'
    }
    
    # Extract version number (e.g., 4.3, 3.5.1)
    version_match = re.search(r'(\d+\.\d+(?:\.\d+)?)', version_string)
    if version_match:
        details['full_version'] = version_match.group(1)
        details['major_version'] = version_match.group(1).split('.')[0]
    
    # Extract build type
    if 'stable' in version_string.lower():
        details['build_type'] = 'Stable'
    elif 'beta' in version_string.lower():
        details['build_type'] = 'Beta'
    elif 'alpha' in version_string.lower():
        details['build_type'] = 'Alpha'
    elif 'rc' in version_string.lower():
        details['build_type'] = 'Release Candidate'
    
    return details

def main():
    """Main function to check desktop for Godot."""
    print("🎮 DESKTOP GODOT INSTALLATION CHECK")
    print("=" * 60)
    
    # Check the desktop directory
    godot_files, exe_files, all_files = check_desktop_directory()
    
    # Test potential Godot executables
    working_godot = test_godot_executables(godot_files, exe_files)
    
    # Final results
    print(f"\n" + "=" * 60)
    print("📋 FINAL RESULTS")
    print("=" * 60)
    
    if working_godot:
        print(f"\n🎉 Found {len(working_godot)} working Godot installation(s):")
        
        for i, godot in enumerate(working_godot, 1):
            details = extract_version_details(godot['version'])
            
            print(f"\n{i}. {godot['name']}")
            print(f"   📁 Path: {godot['path']}")
            print(f"   📦 Size: {godot['size_mb']:.1f} MB")
            print(f"   🏷️  Version: {details['full_version']}")
            print(f"   🔖 Build: {details['build_type']}")
            print(f"   📝 Full: {godot['version']}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        latest = working_godot[0]  # Assume first is latest/best
        print(f"🚀 Recommended: {latest['name']}")
        print(f"   Use this for MCP integration")
        print(f"   Path: {latest['path']}")
        
        # Check if in PATH
        try:
            result = subprocess.run(['godot', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"   ✅ Already accessible via 'godot' command")
            else:
                print(f"   ⚠️  Not in PATH - add directory to PATH")
        except FileNotFoundError:
            print(f"   ⚠️  Not in PATH - add directory to PATH")
    
    else:
        print(f"\n❌ No working Godot installations found on Desktop")
        
        if exe_files:
            print(f"\n📋 Found {len(exe_files)} exe files, but none are Godot:")
            for exe in exe_files:
                print(f"   📄 {exe['name']} ({exe['size_mb']:.1f} MB)")
        
        print(f"\n💡 TO INSTALL GODOT:")
        print(f"1. Go to: https://godotengine.org/download")
        print(f"2. Download Godot 4.x for Windows")
        print(f"3. Save to Desktop or extract there")
        print(f"4. Run this script again to verify")
    
    return working_godot

if __name__ == "__main__":
    installations = main()
    
    if installations:
        print(f"\n🚀 NEXT STEPS:")
        print(f"1. Add Godot directory to PATH if needed")
        print(f"2. Test: godot --version")
        print(f"3. Continue with MCP setup")
    else:
        print(f"\n📥 INSTALL GODOT FIRST:")
        print(f"Download from godotengine.org and save to Desktop")
