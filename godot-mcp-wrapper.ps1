# Godot MCP Wrapper Script
# This script launches the Godot MCP server using Node.js

$NodePath = "node"
$GodotMcpPath = "godot-integration\godot-mcp\build\index.js"

# Check if node exists
try {
    $null = Get-Command node -ErrorAction Stop
    Write-Host "✅ Node.js found"
} catch {
    Write-Error "Node.js not found in PATH. Please install Node.js from: https://nodejs.org/"
    exit 1
}

# Check if Godot MCP server exists
if (-not (Test-Path $GodotMcpPath)) {
    Write-Error "Godot MCP server not found at $GodotMcpPath. Please run setup_godot_mcp.py first"
    exit 1
}

# Launch godot-mcp with node, passing through all arguments
Write-Host "🚀 Starting Godot MCP server..."
& $NodePath $GodotMcpPath $args
