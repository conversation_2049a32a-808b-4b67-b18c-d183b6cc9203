#!/usr/bin/env python3
"""
Set up the latest Godot version (4.4.1) for MCP integration.
"""

import os
import sys
import subprocess
import winreg
import shutil
from pathlib import Path

def add_to_user_path(new_path):
    """Add a path to the user PATH environment variable."""
    try:
        # Get current user PATH
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment") as key:
                current_path = winreg.QueryValueEx(key, "PATH")[0]
        except FileNotFoundError:
            current_path = ""
        
        # Check if path already exists
        path_parts = [p.strip() for p in current_path.split(';') if p.strip()]
        if new_path.lower() in [p.lower() for p in path_parts]:
            print(f"✅ {new_path} already in User PATH")
            return True
        
        # Add new path
        new_full_path = current_path + ";" + new_path if current_path else new_path
        
        # Update registry
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment", 0, winreg.KEY_SET_VALUE) as key:
            winreg.SetValueEx(key, "PATH", 0, winreg.REG_EXPAND_SZ, new_full_path)
        
        print(f"✅ Added {new_path} to User PATH")
        
        # Update current session
        os.environ["PATH"] = os.environ.get("PATH", "") + ";" + new_path
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to add {new_path} to User PATH: {e}")
        return False

def create_godot_alias():
    """Create a batch file to make 'godot' command work."""
    desktop_path = r"C:\Users\<USER>\OneDrive\Desktop"
    godot_exe = os.path.join(desktop_path, "Godot_v4.4.1-stable_win64.exe")
    
    # Create a batch file in the desktop directory
    batch_content = f'''@echo off
REM Godot 4.4.1 Launcher
"{godot_exe}" %*
'''
    
    batch_path = os.path.join(desktop_path, "godot.bat")
    
    try:
        with open(batch_path, 'w') as f:
            f.write(batch_content)
        print(f"✅ Created godot.bat launcher: {batch_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to create batch file: {e}")
        return False

def test_all_godot_versions():
    """Test all Godot versions found."""
    desktop_path = r"C:\Users\<USER>\OneDrive\Desktop"
    
    godot_versions = [
        ("Godot 4.3", "Godot_v4.3-stable_win64.exe"),
        ("Godot 4.4", "Godot_v4.4-stable_win64.exe"),
        ("Godot 4.4.1", "Godot_v4.4.1-stable_win64.exe")
    ]
    
    print("🧪 Testing all Godot versions...")
    
    working_versions = []
    
    for name, filename in godot_versions:
        exe_path = os.path.join(desktop_path, filename)
        
        if os.path.exists(exe_path):
            try:
                result = subprocess.run([exe_path, '--version'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    print(f"   ✅ {name}: {version}")
                    working_versions.append({
                        'name': name,
                        'filename': filename,
                        'path': exe_path,
                        'version': version
                    })
                else:
                    print(f"   ❌ {name}: Not working")
            except subprocess.TimeoutExpired:
                print(f"   ⚠️  {name}: Timeout")
            except Exception as e:
                print(f"   ❌ {name}: Error - {e}")
        else:
            print(f"   ❌ {name}: File not found")
    
    return working_versions

def test_godot_command():
    """Test if godot command works."""
    print("\n🔍 Testing 'godot' command...")
    
    # Try different command variations
    commands = ['godot', 'godot.bat', 'Godot_v4.4.1-stable_win64.exe']
    
    for cmd in commands:
        try:
            result = subprocess.run([cmd, '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"   ✅ '{cmd}' works: {version}")
                return True, cmd
        except (FileNotFoundError, subprocess.TimeoutExpired):
            print(f"   ❌ '{cmd}' not found or timeout")
        except Exception as e:
            print(f"   ❌ '{cmd}' error: {e}")
    
    return False, None

def refresh_environment():
    """Refresh environment variables."""
    try:
        # Broadcast environment change
        import ctypes
        from ctypes import wintypes
        
        HWND_BROADCAST = 0xFFFF
        WM_SETTINGCHANGE = 0x001A
        SMTO_ABORTIFHUNG = 0x0002
        
        ctypes.windll.user32.SendMessageTimeoutW(
            HWND_BROADCAST, WM_SETTINGCHANGE, 0, "Environment",
            SMTO_ABORTIFHUNG, 5000, ctypes.byref(wintypes.DWORD())
        )
        print("✅ Environment variables refreshed")
    except Exception:
        print("⚠️  Please restart your terminal/VS Code to apply PATH changes")

def main():
    """Main function to set up the latest Godot version."""
    print("🎮 SETTING UP LATEST GODOT VERSION FOR MCP")
    print("=" * 60)
    
    # Test all versions
    working_versions = test_all_godot_versions()
    
    if not working_versions:
        print("❌ No working Godot versions found")
        return False
    
    print(f"\n✅ Found {len(working_versions)} working Godot versions")
    
    # Use the latest version (4.4.1)
    latest_version = None
    for version in working_versions:
        if "4.4.1" in version['name']:
            latest_version = version
            break
    
    if not latest_version:
        # Fallback to any working version
        latest_version = working_versions[-1]
    
    print(f"\n🚀 Using: {latest_version['name']}")
    print(f"   File: {latest_version['filename']}")
    print(f"   Version: {latest_version['version']}")
    
    # Add desktop to PATH
    desktop_path = r"C:\Users\<USER>\OneDrive\Desktop"
    print(f"\n📁 Adding to PATH: {desktop_path}")
    
    if add_to_user_path(desktop_path):
        print("✅ Desktop added to PATH")
    else:
        print("❌ Failed to add desktop to PATH")
        return False
    
    # Create godot.bat alias
    print(f"\n🔗 Creating 'godot' command alias...")
    if create_godot_alias():
        print("✅ Godot command alias created")
    else:
        print("❌ Failed to create alias")
    
    # Refresh environment
    print(f"\n🔄 Refreshing environment...")
    refresh_environment()
    
    # Test godot command
    godot_works, godot_cmd = test_godot_command()
    
    # Summary
    print(f"\n" + "=" * 60)
    print("📋 SETUP SUMMARY")
    print("=" * 60)
    
    print(f"✅ Latest Godot: {latest_version['name']}")
    print(f"✅ Desktop in PATH: {desktop_path}")
    print(f"✅ Godot alias: godot.bat created")
    print(f"{'✅' if godot_works else '⚠️ '} Godot command: {'Working' if godot_works else 'Restart needed'}")
    
    print(f"\n🎯 GODOT VERSIONS AVAILABLE:")
    for version in working_versions:
        print(f"   🎮 {version['name']}: {version['filename']}")
    
    print(f"\n🚀 NEXT STEPS:")
    if godot_works:
        print("1. ✅ Godot is ready!")
        print("2. Run: python complete_godot_mcp_setup.py")
        print("3. Start using Godot MCP with Augment Code")
    else:
        print("1. Restart your terminal/VS Code")
        print("2. Test: godot --version")
        print("3. Run: python complete_godot_mcp_setup.py")
    
    print(f"\n💡 USAGE:")
    print(f"   - Use 'godot' command for latest version (4.4.1)")
    print(f"   - Or use specific version: 'Godot_v4.3-stable_win64.exe'")
    print(f"   - All versions are in: {desktop_path}")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n🎉 Godot setup complete! Ready for MCP integration!")
    else:
        print(f"\n❌ Setup failed - please check errors above")
