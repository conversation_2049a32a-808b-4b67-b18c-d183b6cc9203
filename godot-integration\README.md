# Godot MCP Integration for SimRace Godot Project

This directory contains the Godot Model Context Protocol (MCP) integration for your SimRace Godot project, enabling AI-assisted game development and seamless interaction between Augment Code and Godot.

## Overview

The Godot MCP integration allows you to:
- Use AI (Claude/Augment Code) to interact with Godot projects
- Launch Godot editor and run projects programmatically
- Create and manage scenes, nodes, and game objects
- Debug and analyze Godot projects with AI assistance
- Automate common Godot development tasks

## Prerequisites

Before using this integration, ensure you have:

1. **Node.js** (v14 or later) - [Download here](https://nodejs.org/)
2. **Git** - [Download here](https://git-scm.com/)
3. **Godot Engine** (v4.0 or later) - [Download here](https://godotengine.org/)
4. **VS Code** with Augment Code extension
5. **A Godot project** in your workspace

## Quick Setup

Run the automated setup script from your project root:

```bash
python setup_godot_mcp.py
```

This script will:
- Check for required dependencies
- <PERSON><PERSON> and build the Godot MCP server
- Configure VS Code settings
- Create wrapper scripts for easy execution

## Manual Setup

If you prefer to set up manually:

### 1. Install Godot MCP Server

```bash
# Create integration directory
mkdir -p godot-integration
cd godot-integration

# Clone the Godot MCP server
git clone https://github.com/Coding-Solo/godot-mcp.git
cd godot-mcp

# Install dependencies and build
npm install
npm run build
```

### 2. Configure Augment Code

#### Option A: Using VS Code Settings UI

1. Open VS Code with the Augment Code extension
2. Open the Augment panel
3. Click the gear icon (⚙️) in the upper right of the Augment panel
4. In the MCP section, click "New MCP Server"
5. Fill in the configuration:
   - **Name**: `godot`
   - **Command**: `node`
   - **Args**: `["/absolute/path/to/godot-integration/godot-mcp/build/index.js"]`
6. Click "Add" to save the configuration

#### Option B: Using settings.json

1. Press `Cmd/Ctrl + Shift + P` in VS Code
2. Search for "Augment: Edit Settings"
3. Under Advanced, click "Edit in settings.json"
4. Add the following to the `augment.advanced` object:

```json
"augment.advanced": {
    "mcpServers": [
        {
            "name": "godot",
            "command": "node",
            "args": ["/absolute/path/to/godot-integration/godot-mcp/build/index.js"]
        }
    ]
}
```

## Available Tools

The Godot MCP server provides these tools:

### Project Management
- **launch_editor** - Launch Godot editor for a project
- **run_project** - Run a Godot project in debug mode
- **stop_project** - Stop a running Godot project
- **get_debug_output** - Capture console output and errors
- **get_godot_version** - Get installed Godot version
- **list_projects** - Find Godot projects in a directory
- **get_project_info** - Get detailed project information

### Scene Management
- **create_scene** - Create new scenes with specified root nodes
- **add_node** - Add nodes to existing scenes
- **load_sprite** - Load sprites and textures into Sprite2D nodes
- **save_scene** - Save scenes with options for variants
- **export_mesh_library** - Export 3D scenes as MeshLibrary resources

### Advanced Features (Godot 4.4+)
- **get_uid** - Get UID for specific files
- **update_project_uids** - Update UID references by resaving resources

## Usage Examples

Once configured, you can use natural language prompts with Augment Agent:

### Basic Project Operations
```
"Launch the Godot editor for my project"
"Run my Godot project and show me any errors"
"Get information about my project structure"
"What version of Godot am I using?"
```

### Scene and Node Creation
```
"Create a new scene with a CharacterBody2D root node"
"Add a Sprite2D node to my player scene"
"Create a UI scene with buttons for the main menu"
"Load the player texture into my character sprite"
```

### Development Assistance
```
"Help me debug this GDScript error: [paste error]"
"Write a player controller script with jump mechanics"
"Create a simple enemy AI script"
"Analyze my project structure and suggest improvements"
```

### Advanced Operations
```
"Export my 3D models as a MeshLibrary for GridMap"
"Create a tilemap scene for my 2D platformer"
"Set up a basic inventory system"
"Create particle effects for my game"
```

## Environment Variables

You can customize the server behavior with these environment variables:

- `GODOT_PATH` - Path to the Godot executable (overrides automatic detection)
- `DEBUG` - Set to "true" to enable detailed server-side debug logging

Example configuration with environment variables:

```json
{
    "name": "godot",
    "command": "node",
    "args": ["/path/to/godot-mcp/build/index.js"],
    "env": {
        "GODOT_PATH": "/path/to/godot/executable",
        "DEBUG": "true"
    }
}
```

## Troubleshooting

### Common Issues

**Godot MCP server not found**
- Ensure Node.js is installed and in PATH
- Verify the build was successful: `npm run build` in the godot-mcp directory
- Check the path in your VS Code settings

**Godot executable not found**
- Install Godot from [godotengine.org](https://godotengine.org/)
- Add Godot to your system PATH, or set the `GODOT_PATH` environment variable
- Verify installation: `godot --version`

**Connection issues**
- Restart VS Code after configuration changes
- Check that the MCP server is properly configured in Augment settings
- Look for error messages in the VS Code developer console

**Project not recognized**
- Ensure your directory contains a `project.godot` file
- Use absolute paths when specifying project directories
- Check file permissions

### Debug Mode

Enable debug mode for detailed logging:

```json
{
    "name": "godot",
    "command": "node",
    "args": ["/path/to/godot-mcp/build/index.js"],
    "env": {
        "DEBUG": "true"
    }
}
```

## Integration with SimRace Project

This Godot MCP integration is specifically configured for your SimRace project:

- **Racing Game Development**: Create vehicle controllers, track systems, and racing mechanics
- **3D Asset Pipeline**: Manage 3D models, textures, and materials for racing environments
- **UI Systems**: Build racing HUDs, menus, and interface elements
- **Physics Integration**: Set up realistic vehicle physics and collision systems
- **Performance Optimization**: Analyze and optimize racing game performance

## Contributing

To contribute to the Godot MCP server:

1. Visit the [Godot MCP repository](https://github.com/Coding-Solo/godot-mcp)
2. Follow the contribution guidelines
3. Submit issues or pull requests

## Resources

- [Godot MCP Server Documentation](https://github.com/Coding-Solo/godot-mcp)
- [Model Context Protocol Specification](https://modelcontextprotocol.io/)
- [Godot Engine Documentation](https://docs.godotengine.org/)
- [Augment Code Extension](https://marketplace.visualstudio.com/items?itemName=augmentcode.augment-code)

## License

This integration uses the Godot MCP server which is licensed under the MIT License.
