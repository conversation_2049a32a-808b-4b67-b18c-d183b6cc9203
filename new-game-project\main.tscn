[gd_scene load_steps=2 format=3 uid="uid://bvnxm8qfqkxqy"]

[ext_resource type="Texture2D" uid="uid://bvnxm8qfqkxqy" path="res://icon.svg" id="1_8aniq"]

[node name="Main" type="Node2D"]

[node name="Icon" type="Sprite2D" parent="."]
position = Vector2(576, 324)
texture = ExtResource("1_8aniq")

[node name="Label" type="Label" parent="."]
offset_left = 400.0
offset_top = 100.0
offset_right = 752.0
offset_bottom = 150.0
text = "Hello from Godot via MCP!"
horizontal_alignment = 1
