[gd_scene load_steps=1 format=3 uid="uid://bvnxm8qfqkxqy"]

[node name="Main" type="Node2D"]

[node name="Label" type="Label" parent="."]
offset_left = 400.0
offset_top = 300.0
offset_right = 752.0
offset_bottom = 350.0
text = "Hello from Godot via MCP!"
horizontal_alignment = 1

[node name="ColorRect" type="ColorRect" parent="."]
offset_left = 200.0
offset_top = 200.0
offset_right = 600.0
offset_bottom = 400.0
color = Color(0.2, 0.6, 1, 1)
