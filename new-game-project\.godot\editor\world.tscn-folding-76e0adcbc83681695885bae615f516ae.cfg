[folding]

node_unfolds=[<PERSON>dePath("Ground"), PackedStringArray("surface_material_override"), NodePath("Camera3D"), PackedStringArray("Transform"), NodePath("DirectionalLight3D"), PackedStringArray("Transform", "Light", "Shadow")]
resource_unfolds=["res://world.tscn::PlaneMesh_1", PackedStringArray("Resource"), "res://world.tscn::StandardMaterial3D_1", PackedStringArray("Resource", "Albedo", "Roughness", "UV1")]
nodes_folded=[]
