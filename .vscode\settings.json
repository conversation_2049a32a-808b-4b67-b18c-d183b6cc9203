{"augment.advanced": {"mcpServers": [{"name": "blender", "command": "C:\\Users\\<USER>\\.local\\bin\\uvx.exe", "args": ["blender-mcp"]}, {"name": "godot", "command": "node", "args": ["godot-integration/godot-mcp/build/index.js"], "env": {"DEBUG": "true"}}]}, "files.associations": {"*.gd": "gdscript", "*.cs": "csharp", "project.godot": "ini"}, "godot_tools.editor_path": "", "godot_tools.gdscript_lsp_server_port": 6005, "files.exclude": {"**/.godot": true, "**/build": true, "**/.import": true}, "search.exclude": {"**/.godot": true, "**/build": true, "**/.import": true}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}}