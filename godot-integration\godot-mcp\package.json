{"name": "godot-mcp", "version": "0.1.0", "description": "MCP server for interfacing with Godot game engine. Provides tools for launching the editor, running projects, and capturing debug output.", "type": "module", "bin": {"godot-mcp": "./build/index.js"}, "files": ["build"], "scripts": {"build": "tsc && node scripts/build.js", "prepare": "npm run build", "watch": "tsc --watch", "inspector": "npx @modelcontextprotocol/inspector build/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "0.6.0", "axios": "^1.7.9", "fs-extra": "^11.2.0"}, "devDependencies": {"@types/node": "^20.11.24", "typescript": "^5.3.3"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Coding-Solo/godot-mcp.git"}, "keywords": ["godot", "mcp", "ai", "claude", "cline"]}