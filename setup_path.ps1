# PowerShell Script to Add Node.js and <PERSON><PERSON> to PATH
# Run this script as Administrator for system-wide changes

param(
    [switch]$UserOnly = $false
)

Write-Host "🔧 Setting up Node.js and Godot PATH configuration" -ForegroundColor Green
Write-Host "=" * 60

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to add path to environment
function Add-ToPath {
    param(
        [string]$PathToAdd,
        [string]$Scope = "Machine"
    )
    
    if (-not (Test-Path $PathToAdd)) {
        Write-Host "❌ Path does not exist: $PathToAdd" -ForegroundColor Red
        return $false
    }
    
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", $Scope)
    
    if ($currentPath -split ';' -contains $PathToAdd) {
        Write-Host "✅ Path already exists: $PathToAdd" -ForegroundColor Green
        return $true
    }
    
    try {
        $newPath = $currentPath + ";" + $PathToAdd
        [Environment]::SetEnvironmentVariable("PATH", $newPath, $Scope)
        Write-Host "✅ Added to PATH: $PathToAdd" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ Failed to add path: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to find Node.js installation
function Find-NodeJS {
    Write-Host "`n🔍 Searching for Node.js installation..." -ForegroundColor Yellow
    
    # Check if already in PATH
    try {
        $nodeVersion = node --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            $nodePath = (Get-Command node).Source
            Write-Host "✅ Node.js found in PATH: $nodeVersion" -ForegroundColor Green
            Write-Host "   Location: $nodePath" -ForegroundColor Gray
            return $true, (Split-Path $nodePath -Parent)
        }
    }
    catch { }
    
    # Common installation paths
    $possiblePaths = @(
        "${env:ProgramFiles}\nodejs",
        "${env:ProgramFiles(x86)}\nodejs",
        "$env:USERPROFILE\AppData\Roaming\npm",
        "$env:USERPROFILE\AppData\Local\Programs\nodejs"
    )
    
    foreach ($path in $possiblePaths) {
        $nodeExe = Join-Path $path "node.exe"
        if (Test-Path $nodeExe) {
            try {
                $version = & $nodeExe --version 2>$null
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✅ Found Node.js: $version" -ForegroundColor Green
                    Write-Host "   Location: $path" -ForegroundColor Gray
                    return $true, $path
                }
            }
            catch { }
        }
    }
    
    Write-Host "❌ Node.js not found" -ForegroundColor Red
    return $false, $null
}

# Function to find Godot installation
function Find-Godot {
    Write-Host "`n🔍 Searching for Godot installation..." -ForegroundColor Yellow
    
    # Check if already in PATH
    $godotCommands = @("godot", "godot4", "Godot")
    foreach ($cmd in $godotCommands) {
        try {
            $godotVersion = & $cmd --version 2>$null
            if ($LASTEXITCODE -eq 0) {
                $godotPath = (Get-Command $cmd).Source
                Write-Host "✅ Godot found in PATH: $godotVersion" -ForegroundColor Green
                Write-Host "   Location: $godotPath" -ForegroundColor Gray
                return $true, (Split-Path $godotPath -Parent)
            }
        }
        catch { }
    }
    
    # Common installation paths and search locations
    $searchPaths = @(
        "${env:ProgramFiles}\Godot",
        "${env:ProgramFiles(x86)}\Godot",
        "$env:USERPROFILE\Downloads",
        "$env:USERPROFILE\Desktop",
        "$env:USERPROFILE\AppData\Local\Programs\Godot",
        "C:\Godot",
        "D:\Godot"
    )
    
    $godotExecutables = @()
    
    foreach ($basePath in $searchPaths) {
        if (Test-Path $basePath) {
            $executables = Get-ChildItem -Path $basePath -Recurse -Include "godot*.exe", "Godot*.exe" -ErrorAction SilentlyContinue
            foreach ($exe in $executables) {
                try {
                    $version = & $exe.FullName --version 2>$null
                    if ($LASTEXITCODE -eq 0) {
                        $godotExecutables += @{
                            Path = $exe.DirectoryName
                            Executable = $exe.FullName
                            Version = $version
                        }
                    }
                }
                catch { }
            }
        }
    }
    
    if ($godotExecutables.Count -gt 0) {
        Write-Host "✅ Found Godot installation(s):" -ForegroundColor Green
        for ($i = 0; $i -lt $godotExecutables.Count; $i++) {
            $godot = $godotExecutables[$i]
            Write-Host "   $($i + 1). $($godot.Executable) ($($godot.Version))" -ForegroundColor Gray
        }
        
        # Use the first one found
        return $true, $godotExecutables[0].Path
    }
    
    Write-Host "❌ Godot not found" -ForegroundColor Red
    return $false, $null
}

# Function to show installation instructions
function Show-InstallationInstructions {
    param([string]$Software)
    
    Write-Host "`n📋 $Software Installation Instructions:" -ForegroundColor Yellow
    
    if ($Software -eq "Node.js") {
        Write-Host "1. Go to: https://nodejs.org/" -ForegroundColor White
        Write-Host "2. Download the LTS version for Windows" -ForegroundColor White
        Write-Host "3. Run the installer with default settings" -ForegroundColor White
        Write-Host "4. Restart your terminal/PowerShell" -ForegroundColor White
    }
    elseif ($Software -eq "Godot") {
        Write-Host "1. Go to: https://godotengine.org/download" -ForegroundColor White
        Write-Host "2. Download Godot 4.x for Windows" -ForegroundColor White
        Write-Host "3. Extract the ZIP file to a folder (e.g., C:\Godot)" -ForegroundColor White
        Write-Host "4. Remember the extraction path" -ForegroundColor White
    }
}

# Main execution
$isAdmin = Test-Administrator
$scope = if ($UserOnly -or -not $isAdmin) { "User" } else { "Machine" }

if (-not $isAdmin -and -not $UserOnly) {
    Write-Host "⚠️  Not running as Administrator. Will modify User PATH only." -ForegroundColor Yellow
    Write-Host "   To modify System PATH, run as Administrator or use -UserOnly flag" -ForegroundColor Yellow
    $scope = "User"
}

Write-Host "📍 Modifying $scope PATH" -ForegroundColor Cyan

# Check and setup Node.js
$nodeFound, $nodePath = Find-NodeJS
if ($nodeFound -and $nodePath) {
    Add-ToPath -PathToAdd $nodePath -Scope $scope | Out-Null
} else {
    Show-InstallationInstructions -Software "Node.js"
}

# Check and setup Godot
$godotFound, $godotPath = Find-Godot
if ($godotFound -and $godotPath) {
    Add-ToPath -PathToAdd $godotPath -Scope $scope | Out-Null
} else {
    Show-InstallationInstructions -Software "Godot"
}

# Refresh environment for current session
$env:PATH = [Environment]::GetEnvironmentVariable("PATH", "Machine") + ";" + [Environment]::GetEnvironmentVariable("PATH", "User")

Write-Host "`n" + "=" * 60
Write-Host "🎉 PATH SETUP COMPLETE!" -ForegroundColor Green
Write-Host "=" * 60

# Final verification
Write-Host "`n🔍 Final verification:" -ForegroundColor Yellow

try {
    $nodeVer = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Node.js: $nodeVer" -ForegroundColor Green
    } else {
        Write-Host "❌ Node.js: Not accessible" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Node.js: Not accessible" -ForegroundColor Red
}

$godotFound = $false
foreach ($cmd in @("godot", "godot4", "Godot")) {
    try {
        $godotVer = & $cmd --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Godot: $godotVer" -ForegroundColor Green
            $godotFound = $true
            break
        }
    } catch { }
}

if (-not $godotFound) {
    Write-Host "❌ Godot: Not accessible" -ForegroundColor Red
}

Write-Host "`n📝 Next steps:" -ForegroundColor Yellow
Write-Host "1. Restart your terminal/PowerShell/VS Code" -ForegroundColor White
Write-Host "2. Test commands: 'node --version' and 'godot --version'" -ForegroundColor White
Write-Host "3. If successful, run: python setup_godot_mcp.py" -ForegroundColor White

if (-not $nodeFound -or -not $godotFound) {
    Write-Host "`n⚠️  Some software needs to be installed manually." -ForegroundColor Yellow
    Write-Host "   Follow the installation instructions above." -ForegroundColor Yellow
}
