#!/usr/bin/env python3
"""
Thorough and comprehensive search for Godot installations.
This script searches extensively across the entire system for any Godot installations.
"""

import os
import sys
import subprocess
import winreg
import shutil
from pathlib import Path
import time

def search_registry_for_godot():
    """Search Windows registry for Godot installations."""
    print("🔍 Searching Windows Registry for Godot...")
    
    godot_entries = []
    
    # Registry locations to search
    registry_locations = [
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"),
        (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Classes\Applications"),
        (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Classes\Applications"),
    ]
    
    for hkey, subkey_path in registry_locations:
        try:
            with winreg.OpenKey(hkey, subkey_path) as key:
                i = 0
                while True:
                    try:
                        subkey_name = winreg.EnumKey(key, i)
                        try:
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                # Check DisplayName
                                try:
                                    display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                    if "godot" in display_name.lower():
                                        print(f"   📋 Found in registry: {display_name}")
                                        try:
                                            install_location = winreg.QueryValueEx(subkey, "InstallLocation")[0]
                                            godot_entries.append(install_location)
                                            print(f"      Install location: {install_location}")
                                        except FileNotFoundError:
                                            pass
                                except FileNotFoundError:
                                    pass
                                
                                # Check if subkey name contains godot
                                if "godot" in subkey_name.lower():
                                    print(f"   📋 Found registry key: {subkey_name}")
                        except PermissionError:
                            pass
                        i += 1
                    except OSError:
                        break
        except Exception:
            continue
    
    return godot_entries

def search_common_locations():
    """Search common installation locations."""
    print("\n🔍 Searching common installation locations...")
    
    # Extensive list of possible locations
    search_locations = [
        # Standard program locations
        r"C:\Program Files\Godot",
        r"C:\Program Files (x86)\Godot",
        r"C:\Program Files\GodotEngine",
        r"C:\Program Files (x86)\GodotEngine",
        
        # User directories
        os.path.expanduser(r"~\Downloads"),
        os.path.expanduser(r"~\Desktop"),
        os.path.expanduser(r"~\Documents"),
        os.path.expanduser(r"~\Documents\Godot"),
        os.path.expanduser(r"~\AppData\Local\Programs\Godot"),
        os.path.expanduser(r"~\AppData\Roaming\Godot"),
        os.path.expanduser(r"~\AppData\Local\Godot"),
        
        # Common game/development directories
        r"C:\Godot",
        r"D:\Godot",
        r"C:\Games\Godot",
        r"D:\Games\Godot",
        r"C:\Development\Godot",
        r"D:\Development\Godot",
        r"C:\Tools\Godot",
        r"D:\Tools\Godot",
        r"C:\GameDev\Godot",
        r"D:\GameDev\Godot",
        
        # Portable installations
        r"C:\PortableApps\Godot",
        r"D:\PortableApps\Godot",
        
        # Steam locations (if installed via Steam)
        os.path.expanduser(r"~\AppData\Local\Steam\steamapps\common"),
        r"C:\Program Files (x86)\Steam\steamapps\common",
        r"C:\Program Files\Steam\steamapps\common",
        
        # Other drives
        r"E:\Godot",
        r"F:\Godot",
        r"G:\Godot",
    ]
    
    found_locations = []
    
    for location in search_locations:
        if os.path.exists(location):
            print(f"   📁 Checking: {location}")
            found_locations.append(location)
        else:
            print(f"   ❌ Not found: {location}")
    
    return found_locations

def search_entire_drives():
    """Search entire drives for Godot executables (more intensive)."""
    print("\n🔍 Searching entire drives for Godot executables...")
    print("   ⚠️  This may take a while...")
    
    godot_executables = []
    
    # Get all available drives
    drives = ['C:', 'D:', 'E:', 'F:', 'G:']
    
    for drive in drives:
        if os.path.exists(drive + "\\"):
            print(f"   🔍 Searching drive {drive}")
            
            # Search for Godot executables
            try:
                for root, dirs, files in os.walk(drive + "\\"):
                    # Skip system directories to speed up search
                    dirs[:] = [d for d in dirs if not d.lower().startswith(('windows', 'system', '$', 'recovery'))]
                    
                    # Limit search depth to avoid extremely long searches
                    level = root.replace(drive + "\\", "").count(os.sep)
                    if level > 4:
                        dirs[:] = []
                        continue
                    
                    for file in files:
                        if (file.lower().startswith('godot') and file.lower().endswith('.exe')) or \
                           file.lower() in ['godot.exe', 'godot4.exe', 'godot_v4.exe']:
                            full_path = os.path.join(root, file)
                            print(f"      ✅ Found executable: {full_path}")
                            godot_executables.append(full_path)
                            
                            # Don't search deeper in this directory
                            dirs[:] = []
                            break
                    
                    # Add some progress indication
                    if level == 1:
                        print(f"         Searching {root}...")
                        
            except PermissionError:
                print(f"      ⚠️  Permission denied for some directories on {drive}")
            except Exception as e:
                print(f"      ❌ Error searching {drive}: {e}")
    
    return godot_executables

def find_godot_in_directory(directory):
    """Find Godot executables in a specific directory."""
    godot_executables = []
    
    if not os.path.exists(directory):
        return godot_executables
    
    print(f"   🔍 Searching in: {directory}")
    
    # Search recursively but limit depth
    for root, dirs, files in os.walk(directory):
        # Limit search depth
        level = root.replace(directory, '').count(os.sep)
        if level >= 4:
            dirs[:] = []
            continue
        
        for file in files:
            # Look for various Godot executable patterns
            if (file.lower().startswith('godot') and file.lower().endswith('.exe')) or \
               file.lower() in ['godot.exe', 'godot4.exe', 'godot_v4.exe'] or \
               ('godot' in file.lower() and file.lower().endswith('.exe')):
                
                full_path = os.path.join(root, file)
                print(f"      📄 Found file: {full_path}")
                
                # Test if it's a working Godot executable
                try:
                    result = subprocess.run([full_path, "--version"], 
                                          capture_output=True, text=True, timeout=15)
                    if result.returncode == 0:
                        version = result.stdout.strip()
                        godot_executables.append({
                            'path': root,
                            'executable': full_path,
                            'filename': file,
                            'version': version
                        })
                        print(f"      ✅ Working Godot: {version}")
                    else:
                        print(f"      ❌ Not working: {file}")
                except subprocess.TimeoutExpired:
                    print(f"      ⚠️  Timeout testing: {file}")
                except Exception as e:
                    print(f"      ❌ Error testing {file}: {e}")
    
    return godot_executables

def check_path_for_godot():
    """Check if Godot is already in PATH."""
    print("🔍 Checking if Godot is in PATH...")
    
    godot_commands = ['godot', 'godot4', 'Godot', 'godot.exe', 'godot4.exe']
    
    for cmd in godot_commands:
        godot_path = shutil.which(cmd)
        if godot_path:
            try:
                result = subprocess.run([godot_path, '--version'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    print(f"   ✅ Found in PATH: {cmd} -> {godot_path}")
                    print(f"      Version: {version}")
                    return [{
                        'path': os.path.dirname(godot_path),
                        'executable': godot_path,
                        'filename': os.path.basename(godot_path),
                        'version': version,
                        'in_path': True
                    }]
            except Exception as e:
                print(f"   ❌ Error testing {cmd}: {e}")
    
    print("   ❌ No Godot found in PATH")
    return []

def main():
    """Main comprehensive search function."""
    print("🔍 COMPREHENSIVE GODOT SEARCH")
    print("=" * 60)
    print("This will thoroughly search your system for any Godot installations.")
    print("Please be patient as this may take several minutes...")
    print()
    
    all_godot_installations = []
    
    # 1. Check PATH first
    path_installations = check_path_for_godot()
    all_godot_installations.extend(path_installations)
    
    # 2. Search registry
    registry_locations = search_registry_for_godot()
    
    # 3. Search common locations
    common_locations = search_common_locations()
    
    # 4. Search in found locations
    search_locations = list(set(registry_locations + common_locations))
    
    for location in search_locations:
        if location and os.path.exists(location):
            found_executables = find_godot_in_directory(location)
            all_godot_installations.extend(found_executables)
    
    # 5. Ask user if they want to do a full drive search
    if not all_godot_installations:
        print("\n⚠️  No Godot installations found in common locations.")
        try:
            response = input("Would you like to search entire drives? This may take 10-30 minutes (y/n): ").strip().lower()
            if response in ['y', 'yes']:
                drive_executables = search_entire_drives()
                for exe_path in drive_executables:
                    directory = os.path.dirname(exe_path)
                    filename = os.path.basename(exe_path)
                    try:
                        result = subprocess.run([exe_path, "--version"], 
                                              capture_output=True, text=True, timeout=15)
                        if result.returncode == 0:
                            version = result.stdout.strip()
                            all_godot_installations.append({
                                'path': directory,
                                'executable': exe_path,
                                'filename': filename,
                                'version': version,
                                'in_path': False
                            })
                    except:
                        pass
        except KeyboardInterrupt:
            print("\n⚠️  Drive search cancelled")
    
    # Remove duplicates
    unique_installations = []
    seen_paths = set()
    for inst in all_godot_installations:
        if inst['executable'] not in seen_paths:
            unique_installations.append(inst)
            seen_paths.add(inst['executable'])
    
    # Results
    print("\n" + "=" * 60)
    print("📋 COMPREHENSIVE SEARCH RESULTS")
    print("=" * 60)
    
    if unique_installations:
        print(f"\n🎉 Found {len(unique_installations)} Godot installation(s):")
        
        for i, inst in enumerate(unique_installations, 1):
            print(f"\n{i}. Godot {inst['version']}")
            print(f"   📁 Location: {inst['path']}")
            print(f"   📄 Executable: {inst['executable']}")
            print(f"   🔗 In PATH: {'Yes' if inst.get('in_path', False) else 'No'}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        
        path_installations = [inst for inst in unique_installations if inst.get('in_path', False)]
        if path_installations:
            print("✅ Godot is already accessible from PATH")
        else:
            print("⚠️  Godot found but not in PATH")
            print(f"   Recommended: Add {unique_installations[0]['path']} to PATH")
            print("   Run: python add_nodejs_to_path.py")
        
        print(f"\n🚀 NEXT STEPS:")
        print("1. If not in PATH, add the Godot directory to your PATH")
        print("2. Restart your terminal/VS Code")
        print("3. Test: godot --version")
        print("4. Run: python install_godot_mcp_zip.py")
        
    else:
        print("\n❌ NO GODOT INSTALLATIONS FOUND")
        print("\n📥 To install Godot:")
        print("1. Go to: https://godotengine.org/download")
        print("2. Download Godot 4.x for Windows")
        print("3. Extract to C:\\Godot (or preferred location)")
        print("4. Add the folder to your PATH")
        print("5. Run this search again to verify")
    
    return unique_installations

if __name__ == "__main__":
    main()
