# 🎮 Godot MCP Integration Setup Guide

## Overview

This guide will help you set up the Godot Model Context Protocol (MCP) server integration with Augment Code extension, enabling AI-assisted game development for your SimRace project.

## Prerequisites

Before starting, ensure you have:

- ✅ **Node.js** (v14 or later) - [Download here](https://nodejs.org/)
- ✅ **Git** - [Download here](https://git-scm.com/)
- ✅ **VS Code** with Augment Code extension
- ⚠️ **Godot Engine** (v4.0+) - [Download here](https://godotengine.org/) (optional but recommended)

## Quick Setup

### Option 1: Automated Setup (Recommended)

Run the setup script from your project root:

```bash
python setup_godot_mcp.py
```

This will automatically:
- Check for required dependencies
- Clone and build the Godot MCP server
- Configure VS Code settings
- Create wrapper scripts

### Option 2: Manual Setup

If you prefer manual setup or the automated script fails:

1. **Create integration directory**:
   ```bash
   mkdir godot-integration
   cd godot-integration
   ```

2. **Clone Godot MCP server**:
   ```bash
   git clone https://github.com/Coding-Solo/godot-mcp.git
   cd godot-mcp
   ```

3. **Install and build**:
   ```bash
   npm install
   npm run build
   ```

4. **Update VS Code settings** (`.vscode/settings.json`):
   ```json
   {
       "augment.advanced": {
           "mcpServers": [
               {
                   "name": "godot",
                   "command": "node",
                   "args": ["godot-integration/godot-mcp/build/index.js"],
                   "env": {
                       "DEBUG": "true"
                   }
               }
           ]
       }
   }
   ```

## Verification

After setup, verify the integration:

1. **Check files exist**:
   - `godot-integration/godot-mcp/build/index.js`
   - `.vscode/settings.json` contains Godot MCP configuration

2. **Test the server**:
   ```bash
   node godot-integration/godot-mcp/build/index.js --help
   ```

3. **Restart VS Code** to load the new configuration

## Available Tools

Once configured, you can use these Godot MCP tools through Augment Agent:

### Project Management
- `launch_editor` - Launch Godot editor for a project
- `run_project` - Run a Godot project in debug mode
- `stop_project` - Stop a running Godot project
- `get_debug_output` - Capture console output and errors
- `get_godot_version` - Get installed Godot version
- `list_projects` - Find Godot projects in a directory
- `get_project_info` - Get detailed project information

### Scene Management
- `create_scene` - Create new scenes with specified root nodes
- `add_node` - Add nodes to existing scenes
- `load_sprite` - Load sprites and textures into Sprite2D nodes
- `save_scene` - Save scenes with options for variants
- `export_mesh_library` - Export 3D scenes as MeshLibrary resources

### Advanced Features (Godot 4.4+)
- `get_uid` - Get UID for specific files
- `update_project_uids` - Update UID references

## Usage Examples

### Basic Operations
```
"Launch the Godot editor for my project"
"Run my Godot project and show me any errors"
"Get information about my project structure"
"What version of Godot am I using?"
```

### Scene Creation
```
"Create a new scene with a CharacterBody2D root node"
"Add a Sprite2D node to my player scene"
"Create a UI scene with buttons for the main menu"
"Load the player texture into my character sprite"
```

### Racing Game Development
```
"Create a vehicle controller script with realistic physics"
"Set up a lap timing system with checkpoints"
"Create a speedometer UI for the racing game"
"Design a track with elevation changes and banking"
```

### Development Assistance
```
"Help me debug this GDScript error: [paste error]"
"Analyze my project structure and suggest improvements"
"Create a simple enemy AI script"
"Set up a basic inventory system"
```

## Environment Variables

Customize the server behavior:

- `GODOT_PATH` - Path to Godot executable (overrides auto-detection)
- `DEBUG` - Set to "true" for detailed logging

Example configuration:
```json
{
    "name": "godot",
    "command": "node",
    "args": ["godot-integration/godot-mcp/build/index.js"],
    "env": {
        "GODOT_PATH": "/path/to/godot/executable",
        "DEBUG": "true"
    }
}
```

## Troubleshooting

### Common Issues

**Node.js not found**
- Install Node.js from [nodejs.org](https://nodejs.org/)
- Ensure `node` and `npm` are in your PATH
- Restart your terminal/VS Code after installation

**Git not found**
- Install Git from [git-scm.com](https://git-scm.com/)
- Ensure `git` is in your PATH

**Build fails**
- Check Node.js version: `node --version` (should be v14+)
- Clear npm cache: `npm cache clean --force`
- Delete `node_modules` and run `npm install` again

**Godot MCP server not responding**
- Check the server path in VS Code settings
- Verify the build was successful
- Enable debug mode and check logs
- Restart VS Code

**Godot executable not found**
- Install Godot from [godotengine.org](https://godotengine.org/)
- Add Godot to your system PATH
- Or set `GODOT_PATH` environment variable

### Debug Mode

Enable detailed logging:
```json
{
    "env": {
        "DEBUG": "true"
    }
}
```

Check VS Code developer console for error messages.

## Integration with SimRace Project

This Godot MCP integration enhances your SimRace development:

- **Vehicle Systems**: Create realistic car controllers and physics
- **Track Design**: Build racing circuits with proper geometry
- **UI Development**: Racing HUDs, menus, and interface elements
- **Performance**: Optimize for racing game requirements
- **Debugging**: AI-assisted troubleshooting and optimization

## Next Steps

1. **Restart VS Code** to load the new MCP configuration
2. **Open or create a Godot project** in your workspace
3. **Start using Augment Agent** with natural language prompts
4. **Explore the examples** in the documentation
5. **Join the community** for support and sharing

## Resources

- [Godot MCP Repository](https://github.com/Coding-Solo/godot-mcp)
- [Godot Documentation](https://docs.godotengine.org/)
- [Augment Code Documentation](https://docs.augmentcode.com/)
- [Model Context Protocol](https://modelcontextprotocol.io/)

## Support

If you encounter issues:

1. Check this troubleshooting guide
2. Review the [Godot MCP issues](https://github.com/Coding-Solo/godot-mcp/issues)
3. Ask in the [Augment Code Discord](https://augmentcode.com/discord)
4. Create an issue in this repository

---

**Happy game development!** 🎮✨
