#!/usr/bin/env python3
"""
Find installed Node.js and <PERSON>ot paths and add them to environment PATH.
This script automatically locates installations and modifies the PATH.
"""

import os
import sys
import subprocess
import winreg
import shutil
from pathlib import Path

def check_admin_rights():
    """Check if running with administrator privileges."""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def find_nodejs_installations():
    """Find all Node.js installations on the system."""
    print("🔍 Searching for Node.js installations...")
    
    installations = []
    
    # Common installation paths
    search_paths = [
        r"C:\Program Files\nodejs",
        r"C:\Program Files (x86)\nodejs",
        os.path.expanduser(r"~\AppData\Roaming\npm"),
        os.path.expanduser(r"~\AppData\Local\Programs\nodejs"),
        r"C:\nodejs",
        r"D:\nodejs"
    ]
    
    # Check registry for installed programs
    try:
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall") as key:
            i = 0
            while True:
                try:
                    subkey_name = winreg.EnumKey(key, i)
                    with winreg.OpenKey(key, subkey_name) as subkey:
                        try:
                            display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                            if "node" in display_name.lower():
                                install_location = winreg.QueryValueEx(subkey, "InstallLocation")[0]
                                if install_location and install_location not in search_paths:
                                    search_paths.append(install_location)
                        except FileNotFoundError:
                            pass
                    i += 1
                except OSError:
                    break
    except Exception:
        pass
    
    # Check each path
    for path in search_paths:
        if os.path.exists(path):
            node_exe = os.path.join(path, "node.exe")
            if os.path.exists(node_exe):
                try:
                    result = subprocess.run([node_exe, "--version"], capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        version = result.stdout.strip()
                        installations.append({
                            'path': path,
                            'executable': node_exe,
                            'version': version,
                            'type': 'Node.js'
                        })
                        print(f"✅ Found Node.js {version} at: {path}")
                except Exception:
                    continue
    
    return installations

def find_godot_installations():
    """Find all Godot installations on the system."""
    print("🔍 Searching for Godot installations...")
    
    installations = []
    
    # Common installation and download paths
    search_paths = [
        r"C:\Godot",
        r"D:\Godot",
        r"C:\Program Files\Godot",
        r"C:\Program Files (x86)\Godot",
        os.path.expanduser(r"~\Downloads"),
        os.path.expanduser(r"~\Desktop"),
        os.path.expanduser(r"~\Documents"),
        os.path.expanduser(r"~\AppData\Local\Programs\Godot"),
        r"C:\Games\Godot",
        r"D:\Games\Godot"
    ]
    
    # Search for Godot executables
    godot_patterns = ["godot*.exe", "Godot*.exe"]
    
    for base_path in search_paths:
        if os.path.exists(base_path):
            # Search recursively but limit depth to avoid long searches
            for root, dirs, files in os.walk(base_path):
                # Limit search depth
                level = root.replace(base_path, '').count(os.sep)
                if level >= 3:
                    dirs[:] = []  # Don't go deeper
                    continue
                
                for file in files:
                    if any(file.lower().startswith(pattern.replace('*', '').replace('.exe', '')) and file.lower().endswith('.exe') 
                           for pattern in godot_patterns):
                        full_path = os.path.join(root, file)
                        try:
                            result = subprocess.run([full_path, "--version"], capture_output=True, text=True, timeout=5)
                            if result.returncode == 0:
                                version = result.stdout.strip()
                                installations.append({
                                    'path': root,
                                    'executable': full_path,
                                    'version': version,
                                    'type': 'Godot'
                                })
                                print(f"✅ Found Godot {version} at: {root}")
                        except Exception:
                            continue
    
    return installations

def get_current_path(scope="User"):
    """Get current PATH environment variable."""
    try:
        if scope == "System":
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                               r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment") as key:
                return winreg.QueryValueEx(key, "PATH")[0]
        else:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment") as key:
                try:
                    return winreg.QueryValueEx(key, "PATH")[0]
                except FileNotFoundError:
                    return ""
    except Exception as e:
        print(f"Error reading PATH: {e}")
        return ""

def add_to_path(new_path, scope="User"):
    """Add a path to the environment PATH variable."""
    try:
        current_path = get_current_path(scope)
        
        # Check if path already exists
        path_parts = [p.strip() for p in current_path.split(';') if p.strip()]
        if new_path.lower() in [p.lower() for p in path_parts]:
            print(f"✅ {new_path} already in {scope} PATH")
            return True
        
        # Add new path
        new_full_path = current_path + ";" + new_path if current_path else new_path
        
        if scope == "System":
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                               r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment",
                               0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "PATH", 0, winreg.REG_EXPAND_SZ, new_full_path)
        else:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment", 0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "PATH", 0, winreg.REG_EXPAND_SZ, new_full_path)
        
        print(f"✅ Added {new_path} to {scope} PATH")
        return True
        
    except Exception as e:
        print(f"❌ Failed to add {new_path} to {scope} PATH: {e}")
        return False

def refresh_environment():
    """Refresh environment variables for current session."""
    try:
        # Update current session PATH
        user_path = get_current_path("User")
        system_path = get_current_path("System")
        os.environ["PATH"] = system_path + ";" + user_path
        print("✅ Environment refreshed for current session")
    except Exception as e:
        print(f"⚠️  Could not refresh environment: {e}")

def main():
    """Main function to find and add paths."""
    print("🔧 Finding and Adding Node.js and Godot to PATH")
    print("=" * 60)
    
    is_admin = check_admin_rights()
    scope = "System" if is_admin else "User"
    
    if is_admin:
        print("✅ Running as Administrator - will modify System PATH")
    else:
        print("⚠️  Running as regular user - will modify User PATH only")
        print("   (Run as Administrator to modify System PATH)")
    
    print(f"📍 Target scope: {scope} PATH")
    print()
    
    # Find Node.js installations
    nodejs_installations = find_nodejs_installations()
    
    # Find Godot installations
    godot_installations = find_godot_installations()
    
    print("\n" + "=" * 60)
    print("📋 FOUND INSTALLATIONS")
    print("=" * 60)
    
    if not nodejs_installations and not godot_installations:
        print("❌ No installations found!")
        print("\nPlease install:")
        print("- Node.js from: https://nodejs.org/")
        print("- Godot from: https://godotengine.org/")
        return
    
    # Add Node.js to PATH
    if nodejs_installations:
        print(f"\n📦 Adding Node.js to {scope} PATH...")
        for installation in nodejs_installations:
            add_to_path(installation['path'], scope)
    else:
        print("\n❌ Node.js not found - please install from https://nodejs.org/")
    
    # Add Godot to PATH
    if godot_installations:
        print(f"\n🎮 Adding Godot to {scope} PATH...")
        for installation in godot_installations:
            add_to_path(installation['path'], scope)
    else:
        print("\n❌ Godot not found - please install from https://godotengine.org/")
    
    # Refresh environment
    print(f"\n🔄 Refreshing environment...")
    refresh_environment()
    
    print("\n" + "=" * 60)
    print("🎉 PATH CONFIGURATION COMPLETE!")
    print("=" * 60)
    
    print("\n📝 Next steps:")
    print("1. Restart your terminal/VS Code to pick up PATH changes")
    print("2. Test the installations:")
    print("   - node --version")
    print("   - npm --version")
    print("   - godot --version")
    print("3. Run verification: python verify_setup.py")
    print("4. If successful, run: python setup_godot_mcp.py")
    
    print(f"\n💡 Note: PATH was modified in {scope} scope")
    if not is_admin:
        print("   Run as Administrator to modify System PATH for all users")

if __name__ == "__main__":
    main()
