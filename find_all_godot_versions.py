#!/usr/bin/env python3
"""
Find ALL Godot versions installed on the system.
This script searches comprehensively for every Godot installation.
"""

import os
import sys
import subprocess
import winreg
import shutil
from pathlib import Path
import time

def search_entire_system_for_godot():
    """Search the entire system for ALL Godot executables."""
    print("🔍 COMPREHENSIVE SEARCH FOR ALL GODOT VERSIONS")
    print("=" * 70)
    print("Searching entire system for every Godot installation...")
    print("This will take several minutes but will find everything!")
    print()
    
    all_godot_installations = []
    
    # Get all available drives
    drives = []
    for letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':
        drive = f"{letter}:\\"
        if os.path.exists(drive):
            drives.append(drive)
    
    drive_names = [d.rstrip('\\') for d in drives]
    print(f"📁 Found drives: {drive_names}")
    print()
    
    for drive in drives:
        drive_name = drive.rstrip('\\')
        print(f"🔍 Searching drive {drive_name}")
        drive_installations = search_drive_for_godot(drive)
        all_godot_installations.extend(drive_installations)
        print(f"   Found {len(drive_installations)} Godot installation(s) on {drive_name}")
        print()
    
    return all_godot_installations

def search_drive_for_godot(drive_path):
    """Search a specific drive for Godot executables."""
    godot_installations = []
    
    try:
        for root, dirs, files in os.walk(drive_path):
            # Skip system directories and other locations that won't have Godot
            skip_dirs = [
                'windows', 'system32', 'syswow64', '$recycle.bin', 'system volume information',
                'recovery', 'boot', 'msocache', 'intel', 'amd', 'nvidia', 'temp', 'tmp',
                'cache', 'logs', 'log', '.git', '.svn', 'node_modules', '__pycache__',
                'windows.old', 'perflogs', 'programdata\\microsoft', 'programdata\\intel',
                'programdata\\nvidia', 'appdata\\local\\temp', 'appdata\\local\\microsoft'
            ]
            
            # Filter out directories we don't want to search
            current_path_lower = root.lower()
            dirs[:] = [d for d in dirs if not any(skip in current_path_lower for skip in skip_dirs)]
            
            # Limit search depth to avoid extremely long searches
            relative_path = root.replace(drive_path, '')
            level = relative_path.count(os.sep)
            if level > 6:
                dirs[:] = []
                continue
            
            # Look for Godot executables
            for file in files:
                if is_godot_executable(file):
                    full_path = os.path.join(root, file)
                    
                    # Test if it's a working Godot executable
                    godot_info = test_godot_executable(full_path)
                    if godot_info:
                        godot_installations.append(godot_info)
                        print(f"      ✅ Found: {godot_info['version']} at {root}")
                        
                        # Don't search deeper in this directory tree
                        dirs[:] = []
                        break
            
            # Progress indication for top-level directories
            if level == 1:
                dir_name = os.path.basename(root)
                if dir_name and len(dir_name) < 50:  # Avoid very long directory names
                    print(f"         Searching {dir_name}...")
                    
    except PermissionError:
        print(f"      ⚠️  Permission denied for some directories on {drive_path}")
    except Exception as e:
        print(f"      ❌ Error searching {drive_path}: {e}")
    
    return godot_installations

def is_godot_executable(filename):
    """Check if a filename looks like a Godot executable."""
    filename_lower = filename.lower()
    
    # Various patterns for Godot executables
    godot_patterns = [
        'godot.exe',
        'godot4.exe',
        'godot3.exe',
        'godot2.exe',
        'godot_v',  # Matches Godot_v4.3-stable_win64.exe, etc.
        'godot-',   # Matches godot-3.5.exe, etc.
    ]
    
    # Check if it's an executable and matches Godot patterns
    if filename_lower.endswith('.exe'):
        for pattern in godot_patterns:
            if pattern in filename_lower:
                return True
        
        # Also check if it starts with 'godot'
        if filename_lower.startswith('godot'):
            return True
    
    return False

def test_godot_executable(exe_path):
    """Test if an executable is a working Godot installation."""
    try:
        # Test with --version flag
        result = subprocess.run([exe_path, "--version"], 
                              capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0 and result.stdout.strip():
            version = result.stdout.strip()
            
            # Extract version number if possible
            version_number = extract_version_number(version)
            
            return {
                'path': os.path.dirname(exe_path),
                'executable': exe_path,
                'filename': os.path.basename(exe_path),
                'version': version,
                'version_number': version_number,
                'size': get_file_size(exe_path)
            }
    
    except subprocess.TimeoutExpired:
        # Some Godot versions might hang on --version, try --help
        try:
            result = subprocess.run([exe_path, "--help"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                # If --help works, it's probably Godot
                return {
                    'path': os.path.dirname(exe_path),
                    'executable': exe_path,
                    'filename': os.path.basename(exe_path),
                    'version': 'Unknown version (--version timeout)',
                    'version_number': 'Unknown',
                    'size': get_file_size(exe_path)
                }
        except:
            pass
    
    except Exception:
        pass
    
    return None

def extract_version_number(version_string):
    """Extract version number from version string."""
    import re
    
    # Look for version patterns like 4.3, 3.5.1, etc.
    version_patterns = [
        r'(\d+\.\d+\.\d+)',  # x.y.z
        r'(\d+\.\d+)',       # x.y
        r'v(\d+\.\d+\.\d+)', # vx.y.z
        r'v(\d+\.\d+)',      # vx.y
    ]
    
    for pattern in version_patterns:
        match = re.search(pattern, version_string)
        if match:
            return match.group(1)
    
    return "Unknown"

def get_file_size(file_path):
    """Get file size in MB."""
    try:
        size_bytes = os.path.getsize(file_path)
        size_mb = size_bytes / (1024 * 1024)
        return f"{size_mb:.1f} MB"
    except:
        return "Unknown"

def check_path_for_godot():
    """Check if any Godot versions are in PATH."""
    print("🔍 Checking PATH for Godot installations...")
    
    path_installations = []
    godot_commands = ['godot', 'godot4', 'godot3', 'Godot', 'godot.exe', 'godot4.exe']
    
    for cmd in godot_commands:
        godot_path = shutil.which(cmd)
        if godot_path:
            godot_info = test_godot_executable(godot_path)
            if godot_info:
                godot_info['in_path'] = True
                godot_info['command'] = cmd
                path_installations.append(godot_info)
                print(f"   ✅ Found in PATH: {cmd} -> {godot_info['version']}")
    
    if not path_installations:
        print("   ❌ No Godot found in PATH")
    
    return path_installations

def sort_godot_installations(installations):
    """Sort Godot installations by version number."""
    def version_sort_key(installation):
        version_num = installation.get('version_number', 'Unknown')
        if version_num == 'Unknown':
            return (0, 0, 0)  # Put unknown versions first
        
        try:
            parts = version_num.split('.')
            major = int(parts[0]) if len(parts) > 0 else 0
            minor = int(parts[1]) if len(parts) > 1 else 0
            patch = int(parts[2]) if len(parts) > 2 else 0
            return (major, minor, patch)
        except:
            return (0, 0, 0)
    
    return sorted(installations, key=version_sort_key, reverse=True)

def remove_duplicates(installations):
    """Remove duplicate installations (same executable path)."""
    seen_paths = set()
    unique_installations = []
    
    for inst in installations:
        if inst['executable'] not in seen_paths:
            unique_installations.append(inst)
            seen_paths.add(inst['executable'])
    
    return unique_installations

def main():
    """Main function to find all Godot versions."""
    start_time = time.time()
    
    print("🎮 FINDING ALL GODOT VERSIONS ON YOUR SYSTEM")
    print("=" * 70)
    print("This comprehensive search will find every Godot installation,")
    print("including different versions, portable installations, and more.")
    print()
    
    all_installations = []
    
    # 1. Check PATH first
    path_installations = check_path_for_godot()
    all_installations.extend(path_installations)
    
    # 2. Search entire system
    print("\n🔍 Starting comprehensive system search...")
    print("This may take 10-30 minutes depending on your system...")
    print()
    
    try:
        system_installations = search_entire_system_for_godot()
        all_installations.extend(system_installations)
    except KeyboardInterrupt:
        print("\n⚠️  Search interrupted by user")
        print("Showing results found so far...")
    
    # 3. Remove duplicates and sort
    unique_installations = remove_duplicates(all_installations)
    sorted_installations = sort_godot_installations(unique_installations)
    
    # 4. Display results
    end_time = time.time()
    search_time = end_time - start_time
    
    print("\n" + "=" * 70)
    print("📋 ALL GODOT VERSIONS FOUND")
    print("=" * 70)
    print(f"Search completed in {search_time:.1f} seconds")
    print()
    
    if sorted_installations:
        print(f"🎉 Found {len(sorted_installations)} unique Godot installation(s):")
        print()
        
        for i, inst in enumerate(sorted_installations, 1):
            print(f"{i}. Godot {inst['version_number']} ({inst['size']})")
            print(f"   📄 File: {inst['filename']}")
            print(f"   📁 Location: {inst['path']}")
            print(f"   🔗 In PATH: {'Yes' if inst.get('in_path', False) else 'No'}")
            if inst.get('command'):
                print(f"   💻 Command: {inst['command']}")
            print(f"   📝 Full version: {inst['version']}")
            print()
        
        # Recommendations
        print("💡 RECOMMENDATIONS:")
        print("-" * 30)
        
        # Find the latest version
        latest = sorted_installations[0]
        print(f"🚀 Latest version found: Godot {latest['version_number']}")
        print(f"   Location: {latest['path']}")
        
        if not latest.get('in_path', False):
            print(f"   ⚠️  Not in PATH - consider adding: {latest['path']}")
        else:
            print(f"   ✅ Already accessible via: {latest.get('command', 'godot')}")
        
        # Show version breakdown
        versions = {}
        for inst in sorted_installations:
            ver = inst['version_number']
            if ver not in versions:
                versions[ver] = 0
            versions[ver] += 1
        
        print(f"\n📊 Version breakdown:")
        for version, count in sorted(versions.items(), reverse=True):
            print(f"   Godot {version}: {count} installation(s)")
        
    else:
        print("❌ NO GODOT INSTALLATIONS FOUND")
        print("\n📥 To install Godot:")
        print("1. Go to: https://godotengine.org/download")
        print("2. Download your preferred version")
        print("3. Extract to a folder (e.g., C:\\Godot)")
        print("4. Add to PATH if desired")
    
    return sorted_installations

if __name__ == "__main__":
    installations = main()
    
    if installations:
        print("\n🚀 NEXT STEPS:")
        print("1. Choose which version to use for MCP integration")
        print("2. Add to PATH if not already there")
        print("3. Run: python install_godot_mcp_zip.py")
    
    print(f"\n💾 Results saved to memory for future reference")
