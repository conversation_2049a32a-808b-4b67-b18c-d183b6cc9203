# Manual PATH Setup Guide

This guide will help you manually add Node.js and <PERSON>ot to your Windows PATH environment variable.

## Why Add to PATH?

Adding software to your PATH allows you to run commands from any location in the command prompt or PowerShell, which is required for the Godot MCP integration to work properly.

## Method 1: Using System Properties (Recommended)

### Step 1: Open Environment Variables

1. Press `Windows Key + R` to open the Run dialog
2. Type `sysdm.cpl` and press Enter
3. Click the "Environment Variables" button at the bottom
4. You'll see two sections:
   - **User variables** (affects only your user account)
   - **System variables** (affects all users - requires admin rights)

### Step 2: Edit the PATH Variable

1. In the appropriate section (User or System), find the variable named `Path`
2. Select it and click "Edit"
3. Click "New" to add a new entry
4. Add the paths as described below

## Adding Node.js to PATH

### Find Node.js Installation Path

Node.js is typically installed in one of these locations:

- `C:\Program Files\nodejs`
- `C:\Program Files (x86)\nodejs`
- `%USERPROFILE%\AppData\Roaming\npm` (for npm global packages)

### Verify Node.js Installation

1. Navigate to the installation folder
2. Look for `node.exe` and `npm.cmd`
3. Note the full path to this folder

### Add to PATH

1. In the Environment Variables dialog, add the Node.js installation path
2. Example: `C:\Program Files\nodejs`
3. Click "OK" to save

## Adding Godot to PATH

### Find Godot Installation Path

Godot is typically extracted to a custom location:

- `C:\Godot` (if you extracted it there)
- `%USERPROFILE%\Downloads\Godot` (if downloaded and extracted)
- `%USERPROFILE%\Desktop\Godot` (if extracted to desktop)
- Any custom location where you extracted the Godot ZIP file

### Verify Godot Installation

1. Navigate to the folder where you extracted Godot
2. Look for `godot.exe` or `Godot_v4.x.x_win64.exe`
3. Note the full path to the folder containing this executable

### Add to PATH

1. In the Environment Variables dialog, add the Godot folder path
2. Example: `C:\Godot`
3. Click "OK" to save

## Method 2: Using Command Prompt (Temporary)

For a temporary solution (only for current session):

```cmd
# Add Node.js (replace with your actual path)
set PATH=%PATH%;C:\Program Files\nodejs

# Add Godot (replace with your actual path)
set PATH=%PATH%;C:\Godot
```

## Method 3: Using PowerShell (Temporary)

For a temporary solution (only for current session):

```powershell
# Add Node.js (replace with your actual path)
$env:PATH += ";C:\Program Files\nodejs"

# Add Godot (replace with your actual path)
$env:PATH += ";C:\Godot"
```

## Verification

After adding to PATH, open a new Command Prompt or PowerShell and test:

```cmd
# Test Node.js
node --version
npm --version

# Test Godot
godot --version
```

If successful, you should see version numbers for each command.

## Troubleshooting

### "Command not found" errors

1. **Restart your terminal** - PATH changes require a new session
2. **Check the path** - Ensure the folder contains the executable
3. **Check spelling** - Ensure no typos in the path
4. **Use quotes** - If the path contains spaces, use quotes: `"C:\Program Files\nodejs"`

### Permission issues

1. **Run as Administrator** - Some PATH changes require admin rights
2. **Use User variables** - If you can't modify System variables
3. **Check antivirus** - Some antivirus software blocks PATH modifications

### Node.js specific issues

1. **Multiple installations** - Uninstall old versions first
2. **npm not working** - Ensure both Node.js folder and npm folder are in PATH
3. **Version conflicts** - Use Node Version Manager (nvm) if needed

### Godot specific issues

1. **Wrong executable** - Ensure you're pointing to the correct Godot version
2. **Extracted location** - Remember where you extracted the ZIP file
3. **File permissions** - Ensure the executable has proper permissions

## Alternative: Portable Installation

If you prefer not to modify PATH:

### For Node.js
1. Use a portable Node.js version
2. Create batch files that set PATH temporarily
3. Use full paths in your scripts

### For Godot
1. Create a batch file that runs Godot with full path
2. Use environment variables in your scripts
3. Create shortcuts with full paths

## Automated Scripts

We've provided several automated scripts to help:

1. **setup_path.ps1** - PowerShell script for PATH management
2. **setup_dependencies.bat** - Batch file for dependency checking
3. **setup_system_dependencies.py** - Python script for full setup

Choose the method that works best for your system and permissions.

## Next Steps

After successfully adding both Node.js and Godot to PATH:

1. **Restart your terminal/VS Code**
2. **Verify installations** with version commands
3. **Run the Godot MCP setup**: `python setup_godot_mcp.py`
4. **Follow the integration guide** in `GODOT_MCP_SETUP.md`

## Getting Help

If you encounter issues:

1. Check this troubleshooting section
2. Review the automated scripts for guidance
3. Search for Windows PATH setup tutorials
4. Ask for help in the project issues or community forums

Remember: PATH changes are permanent until you remove them, so keep track of what you add!
