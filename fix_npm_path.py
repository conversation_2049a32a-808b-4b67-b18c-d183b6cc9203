#!/usr/bin/env python3
"""
Fix npm PATH issue and run Godot MCP setup.
"""

import os
import sys
import subprocess
import winreg
import shutil
from pathlib import Path

def refresh_path():
    """Refresh PATH environment variable from registry."""
    try:
        # Get system PATH
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                           r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment") as key:
            system_path = winreg.QueryValueEx(key, "PATH")[0]
    except Exception:
        system_path = ""
    
    try:
        # Get user PATH
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment") as key:
            user_path = winreg.QueryValueEx(key, "PATH")[0]
    except Exception:
        user_path = ""
    
    # Combine and update current environment
    new_path = system_path + ";" + user_path
    os.environ["PATH"] = new_path
    
    return new_path

def find_npm():
    """Find npm executable."""
    print("🔍 Searching for npm...")
    
    # First check if npm is in PATH
    npm_path = shutil.which('npm')
    if npm_path:
        print(f"✅ Found npm in PATH: {npm_path}")
        return npm_path
    
    # Check common npm locations
    node_path = shutil.which('node')
    if node_path:
        node_dir = os.path.dirname(node_path)
        print(f"Node.js directory: {node_dir}")
        
        # Check for npm in the same directory
        npm_candidates = [
            os.path.join(node_dir, 'npm.cmd'),
            os.path.join(node_dir, 'npm.exe'),
            os.path.join(node_dir, 'npm'),
        ]
        
        for npm_candidate in npm_candidates:
            if os.path.exists(npm_candidate):
                print(f"✅ Found npm: {npm_candidate}")
                return npm_candidate
    
    # Check Node.js installation directories
    possible_paths = [
        r"C:\Program Files\nodejs",
        r"C:\Program Files (x86)\nodejs",
        os.path.expanduser(r"~\AppData\Roaming\npm"),
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            npm_candidates = [
                os.path.join(path, 'npm.cmd'),
                os.path.join(path, 'npm.exe'),
                os.path.join(path, 'npm'),
            ]
            
            for npm_candidate in npm_candidates:
                if os.path.exists(npm_candidate):
                    print(f"✅ Found npm: {npm_candidate}")
                    return npm_candidate
    
    print("❌ npm not found")
    return None

def test_npm(npm_path=None):
    """Test npm functionality."""
    if npm_path:
        cmd = [npm_path, '--version']
    else:
        cmd = ['npm', '--version']
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ npm version: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ npm failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ npm error: {e}")
        return False

def install_godot_mcp():
    """Install Godot MCP server directly."""
    print("\n📦 Installing Godot MCP server...")
    
    # Create godot-integration directory
    godot_dir = Path("godot-integration")
    godot_dir.mkdir(exist_ok=True)
    
    # Clone the repository
    try:
        if (godot_dir / "godot-mcp").exists():
            print("Updating existing Godot MCP repository...")
            result = subprocess.run(['git', 'pull'], cwd=godot_dir / "godot-mcp", 
                                  capture_output=True, text=True)
        else:
            print("Cloning Godot MCP repository...")
            result = subprocess.run([
                'git', 'clone', 
                'https://github.com/Coding-Solo/godot-mcp.git'
            ], cwd=godot_dir, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Git operation failed: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ Git is not installed. Please install Git first.")
        return False
    
    # Find npm and install dependencies
    npm_path = find_npm()
    if not npm_path:
        print("❌ Cannot find npm to install dependencies")
        return False
    
    # Install dependencies
    try:
        print("Installing npm dependencies...")
        result = subprocess.run([npm_path, 'install'], cwd=godot_dir / "godot-mcp", 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ npm install failed: {result.stderr}")
            return False
            
        print("Building Godot MCP server...")
        result = subprocess.run([npm_path, 'run', 'build'], cwd=godot_dir / "godot-mcp", 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ npm build failed: {result.stderr}")
            return False
            
        print("✅ Godot MCP server installed and built successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error during installation: {e}")
        return False

def update_vscode_settings():
    """Update VS Code settings for Godot MCP."""
    settings_path = Path(".vscode/settings.json")
    
    try:
        import json
        
        # Read existing settings
        if settings_path.exists():
            with open(settings_path, 'r') as f:
                settings = json.load(f)
        else:
            settings = {}
        
        # Ensure augment.advanced structure exists
        if 'augment.advanced' not in settings:
            settings['augment.advanced'] = {}
        if 'mcpServers' not in settings['augment.advanced']:
            settings['augment.advanced']['mcpServers'] = []
        
        # Check if godot server already exists
        mcp_servers = settings['augment.advanced']['mcpServers']
        godot_server = next((s for s in mcp_servers if s.get('name') == 'godot'), None)
        
        godot_mcp_path = str(Path("godot-integration/godot-mcp/build/index.js").absolute())
        
        if godot_server:
            # Update existing server
            godot_server['command'] = 'node'
            godot_server['args'] = [godot_mcp_path]
        else:
            # Add new server
            mcp_servers.append({
                "name": "godot",
                "command": "node",
                "args": [godot_mcp_path],
                "env": {
                    "DEBUG": "true"
                }
            })
        
        # Write updated settings
        with open(settings_path, 'w') as f:
            json.dump(settings, f, indent=4)
        
        print("✅ VS Code settings updated")
        return True
        
    except Exception as e:
        print(f"❌ Error updating VS Code settings: {e}")
        return False

def main():
    """Main function."""
    print("🔧 Fixing npm and Installing Godot MCP")
    print("=" * 50)
    
    # Refresh PATH
    print("\n1. Refreshing PATH...")
    refresh_path()
    
    # Test Node.js
    print("\n2. Testing Node.js...")
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js: {result.stdout.strip()}")
        else:
            print("❌ Node.js not working")
            return
    except FileNotFoundError:
        print("❌ Node.js not found")
        return
    
    # Find and test npm
    print("\n3. Finding npm...")
    npm_path = find_npm()
    if npm_path and test_npm(npm_path):
        print("✅ npm is working")
    else:
        print("⚠️  npm issues detected, but continuing with installation...")
    
    # Install Godot MCP
    print("\n4. Installing Godot MCP...")
    if install_godot_mcp():
        print("✅ Godot MCP installation successful")
    else:
        print("❌ Godot MCP installation failed")
        return
    
    # Update VS Code settings
    print("\n5. Updating VS Code settings...")
    if update_vscode_settings():
        print("✅ VS Code settings updated")
    else:
        print("❌ Failed to update VS Code settings")
    
    print("\n" + "=" * 50)
    print("🎉 SETUP COMPLETE!")
    print("=" * 50)
    print("\nNext steps:")
    print("1. Restart VS Code to load the new MCP configuration")
    print("2. Open or create a Godot project in your workspace")
    print("3. Use Augment Agent with Godot commands:")
    print("   - 'Launch Godot editor for my project'")
    print("   - 'Create a new scene with a Player node'")
    print("   - 'Help me debug this Godot script'")

if __name__ == "__main__":
    main()
