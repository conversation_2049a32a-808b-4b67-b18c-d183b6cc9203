# Blender MCP Integration for SimRace Godot Project

This directory contains the Blender Model Context Protocol (MCP) integration for your SimRace Godot project, enabling AI-assisted 3D modeling and seamless asset pipeline between Blender and Godot.

## Overview

The Blender MCP integration allows you to:
- Use AI (Claude/Augment Code) to create 3D models in Blender
- Automatically export assets in Godot-compatible formats
- Generate racing-specific assets (cars, tracks, environments)
- Streamline the 3D asset pipeline

## Prerequisites

Before using this integration, ensure you have:

1. **Blender 3.0 or newer** installed
2. **Python 3.10 or newer**
3. **uv package manager** installed:
   - **Windows**: `powershell -c "irm https://astral.sh/uv/install.ps1 | iex"`
   - **Mac**: `brew install uv`
   - **Linux**: See [uv installation guide](https://docs.astral.sh/uv/getting-started/installation/)

## Installation Steps

### 1. Install UV Package Manager

First, install the UV package manager which is required for the Blender MCP server:

**Windows:**
```powershell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

**Mac:**
```bash
brew install uv
```

**Linux:**
See [uv installation guide](https://docs.astral.sh/uv/getting-started/installation/)

### 2. Configure Blender MCP Server in Augment Code

There are two ways to configure the Blender MCP server with Augment Code:

#### Option A: Using Augment Settings Panel (Recommended)

1. Open VS Code with the Augment Code extension
2. Open the Augment panel
3. Click the gear icon (⚙️) in the upper right of the Augment panel
4. In the MCP section, click "New MCP Server"
5. Fill in the configuration:
   - **Name**: `blender`
   - **Command**: `uvx blender-mcp`
6. Click "Add" to save the configuration

#### Option B: Using settings.json

1. Press `Cmd/Ctrl + Shift + P` in VS Code
2. Search for "Augment: Edit Settings"
3. Under Advanced, click "Edit in settings.json"
4. Add the following to the `augment.advanced` object:

```json
"augment.advanced": {
    "mcpServers": [
        {
            "name": "blender",
            "command": "uvx",
            "args": ["blender-mcp"]
        }
    ]
}
```

5. Save the file and restart VS Code

### 3. Install Blender Addon

1. Download the `addon.py` file from the [Blender MCP repository](https://github.com/ahujasid/blender-mcp)
2. Open Blender
3. Go to Edit > Preferences > Add-ons
4. Click "Install..." and select the `addon.py` file
5. Enable the addon by checking "Interface: Blender MCP"

### 4. Configure Godot for Blender Import

Your Godot project is already configured to import `.blend` files. The settings in `project.godot` include:

```ini
[filesystem]
import/blender/enabled=true
```

## Usage

### Starting the Integration

1. **In Blender**:
   - Open Blender
   - Go to the 3D View sidebar (press N)
   - Find the "BlenderMCP" tab
   - Enable "Poly Haven" checkbox for asset downloads (optional)
   - Click "Connect to Claude"

2. **In VS Code with Augment Code**:
   - Open the Augment panel in VS Code
   - Start a new conversation with Augment Agent
   - The MCP server will automatically start when you use AI features
   - Look for the hammer icon (🔨) indicating Blender tools are available
   - You can now use natural language to control Blender through Augment

### Example Commands for Racing Game Assets

Here are some AI prompts you can use to create racing-specific content:

#### Vehicle Creation
- "Create a low-poly racing car with aerodynamic features"
- "Design a Formula 1 style race car with detailed cockpit"
- "Make a rally car with off-road modifications"

#### Track Elements
- "Create a racing track with banking turns and elevation changes"
- "Design track barriers and safety elements"
- "Generate grandstand structures for spectators"

#### Environment Assets
- "Create a racing environment with pit stops and garages"
- "Design racing flags and signage"
- "Generate tire barriers and track-side objects"

#### Lighting and Atmosphere
- "Set up studio lighting for car showcase"
- "Create dramatic racing environment lighting"
- "Add HDRIs for realistic racing track environments"

## Asset Pipeline

### Blender to Godot Workflow

1. **Create/Modify in Blender**: Use AI assistance to create or modify 3D assets
2. **Export**: Assets are automatically exported in glTF format when saved as `.blend`
3. **Import to Godot**: Place `.blend` files in your Godot project directory
4. **Configure**: Adjust import settings in Godot's Import tab
5. **Use**: Reference assets in your racing game scenes

### Recommended Directory Structure

```
new-game-project/
├── assets/
│   ├── vehicles/          # Car models and variants
│   ├── tracks/           # Track pieces and complete tracks
│   ├── environment/      # Environmental objects
│   └── materials/        # Shared materials and textures
├── scenes/
│   ├── vehicles/         # Vehicle scene files
│   ├── tracks/          # Track scene files
│   └── ui/              # UI scenes
└── scripts/             # GDScript files
```

## Troubleshooting

### Common Issues

1. **MCP Server Not Starting**:
   - Ensure `uv` is installed and in your PATH
   - Restart your IDE
   - Check that `.cursor/mcp.json` is properly configured

2. **Blender Addon Not Connecting**:
   - Verify the addon is enabled in Blender preferences
   - Try restarting Blender
   - Check that the MCP server is running

3. **Godot Not Importing Blender Files**:
   - Ensure Blender is installed and detectable by Godot
   - Check Editor Settings > Filesystem > Import > Blender > Blender Path
   - Verify `filesystem/import/blender/enabled=true` in project.godot

### Performance Tips

- Use low-poly models for real-time racing gameplay
- Optimize textures for mobile/web targets if needed
- Use LOD (Level of Detail) models for distant objects
- Consider using Godot's built-in optimization tools

## Advanced Features

### Poly Haven Integration

When enabled, you can request:
- "Download racing track textures from Poly Haven"
- "Get HDRIs suitable for racing environments"
- "Find metal and carbon fiber materials for cars"

### Hyper3D AI Model Generation

Generate custom 3D models:
- "Generate a unique racing helmet design"
- "Create a custom spoiler for the race car"
- "Design racing trophies and awards"

## Contributing

To add new features or improve the integration:

1. Create new utility scripts in `blender-integration/scripts/`
2. Add documentation for new workflows
3. Test with various racing game scenarios
4. Submit improvements to the project

## Resources

- [Blender MCP GitHub Repository](https://github.com/ahujasid/blender-mcp)
- [Godot Blender Import Documentation](https://docs.godotengine.org/en/stable/tutorials/assets_pipeline/importing_3d_scenes/available_formats.html#blend-files)
- [Augment Code Documentation](https://docs.augmentcode.com/)
- [Model Context Protocol Specification](https://modelcontextprotocol.io/)

## License

This integration follows the MIT license of the original Blender MCP project.
