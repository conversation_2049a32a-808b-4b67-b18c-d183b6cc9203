.. _doc_introduction_best_practices:

Introduction
============

This series is a collection of best practices to help you work efficiently with
<PERSON><PERSON>.

<PERSON><PERSON> allows for a great amount of flexibility in how you structure a project's
codebase and break it down into scenes. Each approach has its pros and
cons, and they can be hard to weigh until you've worked with the engine for long enough.

There are always many ways to structure your code and solve specific programming
problems. It would be impossible to cover them all here.

That is why each article starts from a real-world problem. We will break down
each problem in fundamental questions, suggest solutions, analyze the pros and
cons of each option, and highlight the best course of action for the problem at hand.

You should start by reading :ref:`doc_what_are_godot_classes`. It explains how
<PERSON><PERSON>'s nodes and scenes relate to classes and objects in other
Object-Oriented programming languages. It will help you make sense of the rest of the series.

.. note::

   The best practices in <PERSON><PERSON> rely on Object-Oriented design principles. We
   use tools like the `single responsibility
   <https://en.wikipedia.org/wiki/Single_responsibility_principle>`_ principle and
   `encapsulation <https://en.wikipedia.org/wiki/Encapsulation_(computer_programming)>`_.
