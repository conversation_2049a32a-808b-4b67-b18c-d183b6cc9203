#!/usr/bin/env python3
"""
Install Godot MCP server by downloading ZIP (no Git required).
"""

import os
import sys
import subprocess
import shutil
import zipfile
import urllib.request
from pathlib import Path
import json

def download_file(url, filename):
    """Download a file from URL."""
    print(f"Downloading {filename}...")
    try:
        urllib.request.urlretrieve(url, filename)
        print(f"✅ Downloaded {filename}")
        return True
    except Exception as e:
        print(f"❌ Download failed: {e}")
        return False

def extract_zip(zip_path, extract_to):
    """Extract ZIP file."""
    print(f"Extracting {zip_path}...")
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        print(f"✅ Extracted to {extract_to}")
        return True
    except Exception as e:
        print(f"❌ Extraction failed: {e}")
        return False

def find_npm():
    """Find npm executable."""
    # Check if npm is in PATH
    npm_path = shutil.which('npm')
    if npm_path:
        return npm_path
    
    # Check common locations
    possible_paths = [
        r"C:\Program Files\nodejs\npm.cmd",
        r"C:\Program Files (x86)\nodejs\npm.cmd",
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return None

def install_godot_mcp():
    """Install Godot MCP server from ZIP download."""
    print("\n📦 Installing Godot MCP server...")
    
    # Create godot-integration directory
    godot_dir = Path("godot-integration")
    godot_dir.mkdir(exist_ok=True)
    
    # Download URL for the repository ZIP
    zip_url = "https://github.com/Coding-Solo/godot-mcp/archive/refs/heads/main.zip"
    zip_path = godot_dir / "godot-mcp-main.zip"
    
    # Download the ZIP file
    if not download_file(zip_url, zip_path):
        return False
    
    # Extract the ZIP file
    if not extract_zip(zip_path, godot_dir):
        return False
    
    # Rename the extracted folder
    extracted_folder = godot_dir / "godot-mcp-main"
    target_folder = godot_dir / "godot-mcp"
    
    if target_folder.exists():
        shutil.rmtree(target_folder)
    
    extracted_folder.rename(target_folder)
    
    # Clean up ZIP file
    zip_path.unlink()
    
    print("✅ Repository downloaded and extracted")
    
    # Find npm
    npm_path = find_npm()
    if not npm_path:
        print("❌ Cannot find npm")
        return False
    
    print(f"Using npm: {npm_path}")
    
    # Install dependencies
    try:
        print("Installing npm dependencies...")
        result = subprocess.run([npm_path, 'install'], cwd=target_folder, 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ npm install failed: {result.stderr}")
            return False
        
        print("✅ Dependencies installed")
        
        print("Building Godot MCP server...")
        result = subprocess.run([npm_path, 'run', 'build'], cwd=target_folder, 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ npm build failed: {result.stderr}")
            return False
        
        print("✅ Build completed")
        
        # Verify build output
        build_file = target_folder / "build" / "index.js"
        if build_file.exists():
            print(f"✅ Build output verified: {build_file}")
            return True
        else:
            print("❌ Build output not found")
            return False
            
    except Exception as e:
        print(f"❌ Error during installation: {e}")
        return False

def update_vscode_settings():
    """Update VS Code settings for Godot MCP."""
    settings_path = Path(".vscode/settings.json")
    
    try:
        # Read existing settings
        if settings_path.exists():
            with open(settings_path, 'r') as f:
                settings = json.load(f)
        else:
            settings = {}
        
        # Ensure augment.advanced structure exists
        if 'augment.advanced' not in settings:
            settings['augment.advanced'] = {}
        if 'mcpServers' not in settings['augment.advanced']:
            settings['augment.advanced']['mcpServers'] = []
        
        # Check if godot server already exists
        mcp_servers = settings['augment.advanced']['mcpServers']
        godot_server = next((s for s in mcp_servers if s.get('name') == 'godot'), None)
        
        # Use relative path for portability
        godot_mcp_path = "godot-integration/godot-mcp/build/index.js"
        
        if godot_server:
            # Update existing server
            godot_server['command'] = 'node'
            godot_server['args'] = [godot_mcp_path]
            if 'env' not in godot_server:
                godot_server['env'] = {}
            godot_server['env']['DEBUG'] = 'true'
        else:
            # Add new server
            mcp_servers.append({
                "name": "godot",
                "command": "node",
                "args": [godot_mcp_path],
                "env": {
                    "DEBUG": "true"
                }
            })
        
        # Write updated settings
        with open(settings_path, 'w') as f:
            json.dump(settings, f, indent=4)
        
        print("✅ VS Code settings updated")
        return True
        
    except Exception as e:
        print(f"❌ Error updating VS Code settings: {e}")
        return False

def test_installation():
    """Test the Godot MCP installation."""
    print("\n🔍 Testing installation...")
    
    build_file = Path("godot-integration/godot-mcp/build/index.js")
    if not build_file.exists():
        print("❌ Build file not found")
        return False
    
    try:
        # Test if the server can start (with timeout)
        result = subprocess.run(['node', str(build_file), '--help'], 
                              capture_output=True, text=True, timeout=10)
        print("✅ Godot MCP server can be executed")
        return True
    except subprocess.TimeoutExpired:
        print("✅ Godot MCP server started (timeout expected)")
        return True
    except Exception as e:
        print(f"⚠️  Could not test server: {e}")
        return True  # Don't fail on test issues

def main():
    """Main function."""
    print("🚀 Installing Godot MCP Server (ZIP Download)")
    print("=" * 60)
    
    # Test Node.js
    print("\n1. Testing Node.js...")
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js: {result.stdout.strip()}")
        else:
            print("❌ Node.js not working")
            return
    except FileNotFoundError:
        print("❌ Node.js not found. Please install from https://nodejs.org/")
        return
    
    # Test npm
    print("\n2. Testing npm...")
    npm_path = find_npm()
    if npm_path:
        try:
            result = subprocess.run([npm_path, '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ npm: {result.stdout.strip()}")
            else:
                print("❌ npm not working")
                return
        except Exception as e:
            print(f"❌ npm error: {e}")
            return
    else:
        print("❌ npm not found")
        return
    
    # Install Godot MCP
    print("\n3. Installing Godot MCP...")
    if not install_godot_mcp():
        print("❌ Installation failed")
        return
    
    # Update VS Code settings
    print("\n4. Updating VS Code settings...")
    if not update_vscode_settings():
        print("❌ Failed to update settings")
        return
    
    # Test installation
    print("\n5. Testing installation...")
    test_installation()
    
    print("\n" + "=" * 60)
    print("🎉 GODOT MCP SETUP COMPLETE!")
    print("=" * 60)
    print("\nWhat was installed:")
    print("✅ Godot MCP server downloaded and built")
    print("✅ VS Code settings configured")
    print("✅ MCP server ready for use")
    
    print("\nNext steps:")
    print("1. Restart VS Code to load the new MCP configuration")
    print("2. Open or create a Godot project in your workspace")
    print("3. Use Augment Agent with natural language:")
    print("   - 'Launch Godot editor for my project'")
    print("   - 'Create a new scene with a Player node'")
    print("   - 'Run my Godot project and show errors'")
    print("   - 'Help me debug this GDScript'")
    
    print("\n📖 For more information:")
    print("- See: godot-integration/README.md")
    print("- See: GODOT_MCP_SETUP.md")
    
    print("\n💡 Optional: Install Godot Engine from https://godotengine.org/")
    print("   (Not required for MCP server, but needed for actual game development)")

if __name__ == "__main__":
    main()
