@echo off
REM Setup Dependencies for Godot MCP Integration
REM This script helps configure Node.js and Godot PATH

echo 🔧 Setting up Node.js and Godot dependencies
echo ============================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running as Administrator
    set ADMIN=true
) else (
    echo ⚠️  Not running as Administrator - will modify User PATH only
    echo    To modify System PATH, right-click and "Run as administrator"
    set ADMIN=false
)

echo.
echo 🔍 Checking current installations...
echo.

REM Check Node.js
echo Checking Node.js...
where node >nul 2>nul
if %errorlevel% == 0 (
    for /f "tokens=*" %%i in ('node --version 2^>nul') do set NODE_VERSION=%%i
    echo ✅ Node.js found: !NODE_VERSION!
    for /f "tokens=*" %%i in ('where node') do echo    Location: %%i
    set NODE_OK=true
) else (
    echo ❌ Node.js not found in PATH
    set NODE_OK=false
)

echo.
echo Checking Godot...
where godot >nul 2>nul
if %errorlevel% == 0 (
    for /f "tokens=*" %%i in ('godot --version 2^>nul') do set GODOT_VERSION=%%i
    echo ✅ Godot found: !GODOT_VERSION!
    for /f "tokens=*" %%i in ('where godot') do echo    Location: %%i
    set GODOT_OK=true
) else (
    where godot4 >nul 2>nul
    if %errorlevel% == 0 (
        for /f "tokens=*" %%i in ('godot4 --version 2^>nul') do set GODOT_VERSION=%%i
        echo ✅ Godot found: !GODOT_VERSION!
        for /f "tokens=*" %%i in ('where godot4') do echo    Location: %%i
        set GODOT_OK=true
    ) else (
        echo ❌ Godot not found in PATH
        set GODOT_OK=false
    )
)

echo.
echo ============================================================

if "%NODE_OK%"=="true" if "%GODOT_OK%"=="true" (
    echo 🎉 All dependencies are already configured!
    echo.
    echo Next steps:
    echo 1. Run: python setup_godot_mcp.py
    echo 2. Follow the Godot MCP setup instructions
    goto :end
)

echo.
echo 🔧 Some dependencies need configuration...
echo.

if "%NODE_OK%"=="false" (
    echo 📦 Node.js Setup Required:
    echo 1. Download from: https://nodejs.org/
    echo 2. Install the LTS version
    echo 3. Use default installation settings
    echo 4. Restart your terminal after installation
    echo.
)

if "%GODOT_OK%"=="false" (
    echo 🎮 Godot Setup Required:
    echo 1. Download from: https://godotengine.org/download
    echo 2. Download Godot 4.x for Windows
    echo 3. Extract to a folder like C:\Godot
    echo 4. Add the folder to your PATH
    echo.
)

echo 💡 PATH Configuration Options:
echo.
echo Option 1 - PowerShell Script (Recommended):
echo    powershell -ExecutionPolicy Bypass -File setup_path.ps1
echo.
echo Option 2 - Manual Configuration:
echo    1. Press Win + R, type: sysdm.cpl
echo    2. Click "Environment Variables"
echo    3. Edit the PATH variable
echo    4. Add the installation directories
echo.
echo Option 3 - Python Script:
echo    python setup_system_dependencies.py
echo.

:end
echo.
echo 📝 For detailed instructions, see: GODOT_MCP_SETUP.md
echo.
pause
