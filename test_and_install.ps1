# Test current installations and help install Godot
# This script refreshes the PATH and tests installations

Write-Host "🔧 Testing installations and setting up Godot" -ForegroundColor Green
Write-Host "=" * 60

# Refresh PATH for current session
Write-Host "`n🔄 Refreshing PATH for current session..." -ForegroundColor Yellow
$env:PATH = [Environment]::GetEnvironmentVariable("PATH", "Machine") + ";" + [Environment]::GetEnvironmentVariable("PATH", "User")

# Test Node.js
Write-Host "`n📦 Testing Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
        $nodePath = (Get-Command node).Source
        Write-Host "   Location: $nodePath" -ForegroundColor Gray
        $nodeOK = $true
    } else {
        Write-Host "❌ Node.js: Not working" -ForegroundColor Red
        $nodeOK = $false
    }
} catch {
    Write-Host "❌ Node.js: Not found" -ForegroundColor Red
    $nodeOK = $false
}

# Test npm
Write-Host "`n📦 Testing npm..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ npm: $npmVersion" -ForegroundColor Green
        $npmOK = $true
    } else {
        Write-Host "❌ npm: Not working" -ForegroundColor Red
        $npmOK = $false
    }
} catch {
    Write-Host "❌ npm: Not found" -ForegroundColor Red
    $npmOK = $false
}

# Test Godot
Write-Host "`n🎮 Testing Godot..." -ForegroundColor Yellow
$godotOK = $false
$godotCommands = @("godot", "godot4", "Godot")

foreach ($cmd in $godotCommands) {
    try {
        $godotVersion = & $cmd --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Godot ($cmd): $godotVersion" -ForegroundColor Green
            $godotPath = (Get-Command $cmd).Source
            Write-Host "   Location: $godotPath" -ForegroundColor Gray
            $godotOK = $true
            break
        }
    } catch { }
}

if (-not $godotOK) {
    Write-Host "❌ Godot: Not found in PATH" -ForegroundColor Red
}

# Summary and next steps
Write-Host "`n" + "=" * 60
Write-Host "📋 SUMMARY" -ForegroundColor Cyan
Write-Host "=" * 60

if ($nodeOK -and $npmOK -and $godotOK) {
    Write-Host "🎉 All dependencies are working!" -ForegroundColor Green
    Write-Host "`n✅ Ready to run: python setup_godot_mcp.py" -ForegroundColor Green
} else {
    Write-Host "⚠️  Some dependencies need attention:" -ForegroundColor Yellow
    
    if (-not $nodeOK -or -not $npmOK) {
        Write-Host "- Node.js/npm: May need terminal restart" -ForegroundColor White
    }
    if (-not $godotOK) {
        Write-Host "- Godot: Needs to be installed" -ForegroundColor White
    }
}

# Godot installation helper
if (-not $godotOK) {
    Write-Host "`n🎮 GODOT INSTALLATION HELPER" -ForegroundColor Cyan
    Write-Host "=" * 40
    
    $downloadGodot = Read-Host "`nWould you like help downloading Godot? (y/n)"
    
    if ($downloadGodot -eq 'y' -or $downloadGodot -eq 'Y') {
        Write-Host "`n📥 Godot Download Instructions:" -ForegroundColor Yellow
        Write-Host "1. Opening Godot download page..." -ForegroundColor White
        
        # Open Godot download page
        Start-Process "https://godotengine.org/download"
        
        Write-Host "2. Download Godot 4.x for Windows" -ForegroundColor White
        Write-Host "3. Extract the ZIP file to a folder (recommended: C:\Godot)" -ForegroundColor White
        
        $installPath = Read-Host "`nWhere did you extract Godot? (e.g., C:\Godot)"
        
        if ($installPath -and (Test-Path $installPath)) {
            # Look for Godot executable
            $godotExe = Get-ChildItem -Path $installPath -Include "godot*.exe", "Godot*.exe" -Recurse | Select-Object -First 1
            
            if ($godotExe) {
                Write-Host "✅ Found Godot at: $($godotExe.FullName)" -ForegroundColor Green
                
                # Add to PATH
                $godotDir = $godotExe.DirectoryName
                $currentUserPath = [Environment]::GetEnvironmentVariable("PATH", "User")
                
                if ($currentUserPath -notlike "*$godotDir*") {
                    $newUserPath = $currentUserPath + ";" + $godotDir
                    [Environment]::SetEnvironmentVariable("PATH", $newUserPath, "User")
                    Write-Host "✅ Added $godotDir to User PATH" -ForegroundColor Green
                    
                    # Refresh current session
                    $env:PATH += ";" + $godotDir
                    
                    # Test again
                    try {
                        $testVersion = & $godotExe.FullName --version 2>$null
                        if ($LASTEXITCODE -eq 0) {
                            Write-Host "✅ Godot is now working: $testVersion" -ForegroundColor Green
                            $godotOK = $true
                        }
                    } catch { }
                } else {
                    Write-Host "✅ Godot directory already in PATH" -ForegroundColor Green
                }
            } else {
                Write-Host "❌ Could not find Godot executable in $installPath" -ForegroundColor Red
                Write-Host "   Make sure you extracted the ZIP file correctly" -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ Path not found or not provided" -ForegroundColor Red
        }
    }
}

# Final status
Write-Host "`n" + "=" * 60
Write-Host "🎯 FINAL STATUS" -ForegroundColor Cyan
Write-Host "=" * 60

if ($nodeOK -and $npmOK -and $godotOK) {
    Write-Host "🎉 ALL SYSTEMS GO!" -ForegroundColor Green
    Write-Host "`nNext steps:" -ForegroundColor Yellow
    Write-Host "1. Run: python setup_godot_mcp.py" -ForegroundColor White
    Write-Host "2. Follow the Godot MCP setup guide" -ForegroundColor White
} else {
    Write-Host "⚠️  Please complete the setup:" -ForegroundColor Yellow
    
    if (-not $nodeOK -or -not $npmOK) {
        Write-Host "- Restart your terminal/VS Code for Node.js changes" -ForegroundColor White
    }
    if (-not $godotOK) {
        Write-Host "- Install Godot from https://godotengine.org/download" -ForegroundColor White
        Write-Host "- Extract to a folder and add to PATH" -ForegroundColor White
    }
    
    Write-Host "`nThen run this script again to verify" -ForegroundColor White
}

Write-Host "`n💡 Tip: Restart VS Code to pick up all PATH changes" -ForegroundColor Cyan
