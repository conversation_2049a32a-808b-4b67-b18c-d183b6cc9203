#!/usr/bin/env python3
"""
Verify that the Godot project settings were applied correctly.
"""

import os
from pathlib import Path

def verify_project_settings():
    """Verify the project.godot file contains the expected settings."""
    project_file = Path("new-game-project/project.godot")
    
    print("🔍 VERIFYING PROJECT SETTINGS")
    print("=" * 40)
    
    if not project_file.exists():
        print("❌ Project file not found")
        return False
    
    try:
        with open(project_file, 'r') as f:
            content = f.read()
        
        # Check for key settings
        checks = {
            "Project Name": 'config/name="SimRace - AI-Assisted Racing Game"' in content,
            "Main Scene": 'run/main_scene="res://BasicPlaneScene.tscn"' in content,
            "Engine Version": 'config/features=PackedStringArray("4.4", "Forward Plus")' in content,
            "Display Settings": 'window/size/viewport_width=1920' in content,
            "Input Map": 'accelerate=' in content and 'brake=' in content,
            "Physics Layers": '3d_physics/layer_1="Ground"' in content,
            "Rendering": 'renderer/rendering_method="forward_plus"' in content,
            "Debug Settings": 'gdscript/warnings/enable=true' in content
        }
        
        print("Project settings verification:")
        all_passed = True
        for setting, passed in checks.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {setting}")
            if not passed:
                all_passed = False
        
        # Count total lines
        lines = content.split('\n')
        print(f"\n📊 Project file stats:")
        print(f"   • Total lines: {len(lines)}")
        print(f"   • File size: {len(content)} characters")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error reading project file: {e}")
        return False

def list_project_files():
    """List all files in the project directory."""
    project_dir = Path("new-game-project")
    
    print(f"\n📁 PROJECT FILES")
    print("=" * 40)
    
    if not project_dir.exists():
        print("❌ Project directory not found")
        return
    
    files = list(project_dir.iterdir())
    print(f"Found {len(files)} files/directories:")
    
    for file in sorted(files):
        if file.is_file():
            size = file.stat().st_size
            if size > 1024:
                size_str = f"{size/1024:.1f} KB"
            else:
                size_str = f"{size} bytes"
            print(f"   📄 {file.name} ({size_str})")
        else:
            print(f"   📁 {file.name}/")

def main():
    """Main verification function."""
    print("🔍 GODOT PROJECT SETTINGS VERIFICATION")
    print("=" * 50)
    
    # Verify project settings
    settings_ok = verify_project_settings()
    
    # List project files
    list_project_files()
    
    # Summary
    print(f"\n" + "=" * 50)
    print("📋 VERIFICATION SUMMARY")
    print("=" * 50)
    
    if settings_ok:
        print("🎉 ALL PROJECT SETTINGS VERIFIED!")
        print("✅ Racing game configuration complete")
        print("✅ Input controls configured")
        print("✅ Physics layers defined")
        print("✅ Rendering optimized")
        
        print(f"\n🎛️ TO VIEW IN GODOT:")
        print("1. Open Godot Editor (should already be running)")
        print("2. Go to: Project → Project Settings")
        print("3. Browse through the different sections:")
        print("   • Application → General")
        print("   • Display → Window") 
        print("   • Input Map")
        print("   • Layer Names → 3D Physics")
        print("   • Rendering → Quality")
        
        print(f"\n🎮 READY FOR RACING GAME DEVELOPMENT!")
        
    else:
        print("❌ SOME SETTINGS VERIFICATION FAILED")
        print("💡 Check the project.godot file manually")
        print("🔧 Re-run setup_project_settings.py if needed")
    
    return settings_ok

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ Project settings verification complete!")
    else:
        print(f"\n⚠️  Verification issues found")
