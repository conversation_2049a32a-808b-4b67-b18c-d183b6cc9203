:allow_comments: False

.. _doc_importing_3d_scenes:

Importing 3D scenes
===================

Godot supports importing 3D scenes from various file formats.
This documentation section describes what those formats are, and how to use
them, including exporting with the correct conventions and best practices,
and how to customize the node type using a suffix in the node name.
The import configuration article describes how to customize the
imported data using the import dock, the advanced import settings
dialog, and inherited scenes.

.. toctree::
   :maxdepth: 1
   :name: toc-learn-workflow-assets-importing_3d_scenes

   available_formats
   model_export_considerations
   node_type_customization
   import_configuration

.. seealso::

    3D scenes can be loaded at runtime using
    :ref:`runtime file loading and saving <doc_runtime_file_loading_and_saving_3d_scenes>`,
    including from an exported project.
