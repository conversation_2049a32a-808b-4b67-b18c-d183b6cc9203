#!/usr/bin/env python3
"""
Add Node.js to PATH and help install Godot.
"""

import os
import sys
import subprocess
import winreg
import webbrowser
from pathlib import Path

def add_to_user_path(new_path):
    """Add a path to the user PATH environment variable."""
    try:
        # Get current user PATH
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment") as key:
                current_path = winreg.QueryValueEx(key, "PATH")[0]
        except FileNotFoundError:
            current_path = ""
        
        # Check if path already exists
        path_parts = [p.strip() for p in current_path.split(';') if p.strip()]
        if new_path.lower() in [p.lower() for p in path_parts]:
            print(f"✅ {new_path} already in User PATH")
            return True
        
        # Add new path
        new_full_path = current_path + ";" + new_path if current_path else new_path
        
        # Update registry
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment", 0, winreg.KEY_SET_VALUE) as key:
            winreg.SetValueEx(key, "PATH", 0, winreg.REG_EXPAND_SZ, new_full_path)
        
        print(f"✅ Added {new_path} to User PATH")
        
        # Update current session
        os.environ["PATH"] = os.environ.get("PATH", "") + ";" + new_path
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to add {new_path} to User PATH: {e}")
        return False

def test_nodejs():
    """Test if Node.js is working."""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js: {result.stdout.strip()}")
            return True
        else:
            print("❌ Node.js: Command failed")
            return False
    except FileNotFoundError:
        print("❌ Node.js: Not found in PATH")
        return False

def test_npm():
    """Test if npm is working."""
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ npm: {result.stdout.strip()}")
            return True
        else:
            print("❌ npm: Command failed")
            return False
    except FileNotFoundError:
        print("❌ npm: Not found in PATH")
        return False

def help_install_godot():
    """Help user install Godot."""
    print("\n🎮 GODOT INSTALLATION HELPER")
    print("=" * 40)
    
    print("Godot is not installed on your system.")
    print("\n📋 Installation steps:")
    print("1. Download Godot 4.x from the official website")
    print("2. Extract the ZIP file to a folder (recommended: C:\\Godot)")
    print("3. Add the folder to your PATH")
    
    # Ask if user wants to open download page
    try:
        response = input("\nWould you like to open the Godot download page? (y/n): ").strip().lower()
        if response in ['y', 'yes']:
            print("🌐 Opening Godot download page...")
            webbrowser.open("https://godotengine.org/download")
            
            print("\n📥 After downloading:")
            print("1. Extract the ZIP file to C:\\Godot (or your preferred location)")
            print("2. Remember the extraction path")
            print("3. Run this script again to add Godot to PATH")
            
            # Ask for installation path
            install_path = input("\nIf you've already extracted Godot, enter the path (or press Enter to skip): ").strip()
            if install_path and os.path.exists(install_path):
                return check_godot_in_path(install_path)
    except KeyboardInterrupt:
        print("\n⚠️  Skipped Godot installation")
    
    return False

def check_godot_in_path(godot_path):
    """Check if Godot executable exists and add to PATH."""
    godot_executables = []
    
    # Look for Godot executables
    if os.path.isdir(godot_path):
        for file in os.listdir(godot_path):
            if file.lower().startswith('godot') and file.lower().endswith('.exe'):
                full_path = os.path.join(godot_path, file)
                try:
                    result = subprocess.run([full_path, '--version'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        godot_executables.append((full_path, result.stdout.strip()))
                except:
                    continue
    
    if godot_executables:
        print(f"✅ Found Godot executable(s):")
        for exe, version in godot_executables:
            print(f"   {exe} ({version})")
        
        # Add to PATH
        if add_to_user_path(godot_path):
            print(f"✅ Added {godot_path} to PATH")
            return True
    else:
        print(f"❌ No working Godot executable found in {godot_path}")
    
    return False

def refresh_environment():
    """Refresh environment variables."""
    try:
        # Broadcast environment change
        import ctypes
        from ctypes import wintypes
        
        HWND_BROADCAST = 0xFFFF
        WM_SETTINGCHANGE = 0x001A
        SMTO_ABORTIFHUNG = 0x0002
        
        ctypes.windll.user32.SendMessageTimeoutW(
            HWND_BROADCAST, WM_SETTINGCHANGE, 0, "Environment",
            SMTO_ABORTIFHUNG, 5000, ctypes.byref(wintypes.DWORD())
        )
        print("✅ Environment variables refreshed")
    except Exception:
        print("⚠️  Please restart your terminal/VS Code to apply PATH changes")

def main():
    """Main function."""
    print("🔧 Adding Node.js to PATH and Installing Godot")
    print("=" * 60)
    
    # Add Node.js to PATH
    print("\n1. Adding Node.js to PATH...")
    nodejs_path = r"C:\Program Files\nodejs"
    
    if os.path.exists(nodejs_path):
        if add_to_user_path(nodejs_path):
            print("✅ Node.js added to PATH")
        else:
            print("❌ Failed to add Node.js to PATH")
            return
    else:
        print(f"❌ Node.js not found at {nodejs_path}")
        return
    
    # Test Node.js
    print("\n2. Testing Node.js...")
    node_ok = test_nodejs()
    npm_ok = test_npm()
    
    if not node_ok:
        print("⚠️  Node.js not working yet - may need terminal restart")
    if not npm_ok:
        print("⚠️  npm not working yet - may need terminal restart")
    
    # Help install Godot
    print("\n3. Checking Godot...")
    godot_ok = help_install_godot()
    
    # Refresh environment
    print("\n4. Refreshing environment...")
    refresh_environment()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 SETUP SUMMARY")
    print("=" * 60)
    
    print(f"✅ Node.js path added: {nodejs_path}")
    print(f"{'✅' if node_ok else '⚠️ '} Node.js working: {'Yes' if node_ok else 'Restart needed'}")
    print(f"{'✅' if npm_ok else '⚠️ '} npm working: {'Yes' if npm_ok else 'Restart needed'}")
    print(f"{'✅' if godot_ok else '❌'} Godot installed: {'Yes' if godot_ok else 'Manual installation needed'}")
    
    print("\n📝 Next steps:")
    print("1. Restart your terminal/VS Code")
    print("2. Test commands:")
    print("   - node --version")
    print("   - npm --version")
    if godot_ok:
        print("   - godot --version")
    print("3. If all working, run: python install_godot_mcp_zip.py")
    
    if not godot_ok:
        print("\n💡 For Godot:")
        print("- Download from: https://godotengine.org/download")
        print("- Extract to C:\\Godot")
        print("- Run this script again to add to PATH")

if __name__ == "__main__":
    main()
