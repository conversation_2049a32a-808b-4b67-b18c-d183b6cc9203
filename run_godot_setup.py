#!/usr/bin/env python3
"""
Run Godot MCP setup with refreshed PATH environment.
This script refreshes the PATH and then runs the Godot MCP setup.
"""

import os
import sys
import subprocess
import winreg

def refresh_path():
    """Refresh PATH environment variable from registry."""
    try:
        # Get system PATH
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                           r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment") as key:
            system_path = winreg.QueryValueEx(key, "PATH")[0]
    except Exception:
        system_path = ""
    
    try:
        # Get user PATH
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment") as key:
            user_path = winreg.QueryValueEx(key, "PATH")[0]
    except Exception:
        user_path = ""
    
    # Combine and update current environment
    new_path = system_path + ";" + user_path
    os.environ["PATH"] = new_path
    
    print("✅ PATH refreshed from registry")
    return new_path

def test_nodejs():
    """Test if Node.js is available."""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js: {result.stdout.strip()}")
            return True
        else:
            print("❌ Node.js: Command failed")
            return False
    except FileNotFoundError:
        print("❌ Node.js: Not found")
        return False

def test_npm():
    """Test if npm is available."""
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ npm: {result.stdout.strip()}")
            return True
        else:
            print("❌ npm: Command failed")
            return False
    except FileNotFoundError:
        print("❌ npm: Not found")
        return False

def run_godot_setup():
    """Run the Godot MCP setup script."""
    print("\n🚀 Running Godot MCP setup...")
    try:
        result = subprocess.run([sys.executable, 'setup_godot_mcp.py'], 
                              capture_output=False, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running setup: {e}")
        return False

def main():
    """Main function."""
    print("🔧 Godot MCP Setup with PATH Refresh")
    print("=" * 50)
    
    # Refresh PATH
    print("\n1. Refreshing PATH environment...")
    refresh_path()
    
    # Test dependencies
    print("\n2. Testing dependencies...")
    node_ok = test_nodejs()
    npm_ok = test_npm()
    
    if not node_ok or not npm_ok:
        print("\n❌ Dependencies not ready")
        print("\nPlease:")
        print("1. Install Node.js from: https://nodejs.org/")
        print("2. Restart your terminal/VS Code")
        print("3. Run this script again")
        return
    
    print("\n✅ Dependencies are ready!")
    
    # Run Godot MCP setup
    print("\n3. Running Godot MCP setup...")
    success = run_godot_setup()
    
    if success:
        print("\n🎉 Godot MCP setup completed successfully!")
    else:
        print("\n⚠️  Godot MCP setup encountered issues")
        print("Check the output above for details")

if __name__ == "__main__":
    main()
