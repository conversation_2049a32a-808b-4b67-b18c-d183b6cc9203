#!/usr/bin/env python3
"""
Add the found Godot installation to PATH and complete setup.
"""

import os
import sys
import subprocess
import winreg
from pathlib import Path

def add_to_user_path(new_path):
    """Add a path to the user PATH environment variable."""
    try:
        # Get current user PATH
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment") as key:
                current_path = winreg.QueryValueEx(key, "PATH")[0]
        except FileNotFoundError:
            current_path = ""
        
        # Check if path already exists
        path_parts = [p.strip() for p in current_path.split(';') if p.strip()]
        if new_path.lower() in [p.lower() for p in path_parts]:
            print(f"✅ {new_path} already in User PATH")
            return True
        
        # Add new path
        new_full_path = current_path + ";" + new_path if current_path else new_path
        
        # Update registry
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment", 0, winreg.KEY_SET_VALUE) as key:
            winreg.SetValueEx(key, "PATH", 0, winreg.REG_EXPAND_SZ, new_full_path)
        
        print(f"✅ Added {new_path} to User PATH")
        
        # Update current session
        os.environ["PATH"] = os.environ.get("PATH", "") + ";" + new_path
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to add {new_path} to User PATH: {e}")
        return False

def test_installations():
    """Test if Node.js and Godot are working."""
    print("🔍 Testing installations...")
    
    # Test Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js: {result.stdout.strip()}")
            node_ok = True
        else:
            print("❌ Node.js: Command failed")
            node_ok = False
    except FileNotFoundError:
        print("❌ Node.js: Not found in PATH")
        node_ok = False
    
    # Test npm
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ npm: {result.stdout.strip()}")
            npm_ok = True
        else:
            print("❌ npm: Command failed")
            npm_ok = False
    except FileNotFoundError:
        print("❌ npm: Not found in PATH")
        npm_ok = False
    
    # Test Godot (try different command names)
    godot_commands = ['godot', 'Godot_v4.3-stable_win64', 'Godot_v4.3-stable_win64.exe']
    godot_ok = False
    
    for cmd in godot_commands:
        try:
            result = subprocess.run([cmd, '--version'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ Godot ({cmd}): {result.stdout.strip()}")
                godot_ok = True
                break
        except (FileNotFoundError, subprocess.TimeoutExpired):
            continue
    
    if not godot_ok:
        print("❌ Godot: Not accessible via command line")
    
    return node_ok, npm_ok, godot_ok

def refresh_environment():
    """Refresh environment variables."""
    try:
        # Broadcast environment change
        import ctypes
        from ctypes import wintypes
        
        HWND_BROADCAST = 0xFFFF
        WM_SETTINGCHANGE = 0x001A
        SMTO_ABORTIFHUNG = 0x0002
        
        ctypes.windll.user32.SendMessageTimeoutW(
            HWND_BROADCAST, WM_SETTINGCHANGE, 0, "Environment",
            SMTO_ABORTIFHUNG, 5000, ctypes.byref(wintypes.DWORD())
        )
        print("✅ Environment variables refreshed")
    except Exception:
        print("⚠️  Please restart your terminal/VS Code to apply PATH changes")

def main():
    """Main function to add Godot to PATH and test setup."""
    print("🔧 Adding Godot to PATH and Testing Setup")
    print("=" * 60)
    
    # Add Godot to PATH
    print("\n1. Adding Godot to PATH...")
    godot_path = r"C:\Users\<USER>\OneDrive\Desktop"
    
    if os.path.exists(godot_path):
        godot_exe = os.path.join(godot_path, "Godot_v4.3-stable_win64.exe")
        if os.path.exists(godot_exe):
            print(f"✅ Godot executable confirmed: {godot_exe}")
            if add_to_user_path(godot_path):
                print("✅ Godot directory added to PATH")
            else:
                print("❌ Failed to add Godot to PATH")
                return
        else:
            print(f"❌ Godot executable not found at {godot_exe}")
            return
    else:
        print(f"❌ Godot directory not found at {godot_path}")
        return
    
    # Refresh environment
    print("\n2. Refreshing environment...")
    refresh_environment()
    
    # Test installations
    print("\n3. Testing installations...")
    node_ok, npm_ok, godot_ok = test_installations()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 SETUP SUMMARY")
    print("=" * 60)
    
    print(f"✅ Node.js path: C:\\Program Files\\nodejs")
    print(f"✅ Godot path: {godot_path}")
    print(f"{'✅' if node_ok else '⚠️ '} Node.js working: {'Yes' if node_ok else 'Restart needed'}")
    print(f"{'✅' if npm_ok else '⚠️ '} npm working: {'Yes' if npm_ok else 'Restart needed'}")
    print(f"{'✅' if godot_ok else '⚠️ '} Godot working: {'Yes' if godot_ok else 'Restart needed'}")
    
    # Next steps
    print("\n🚀 NEXT STEPS:")
    
    if node_ok and npm_ok and godot_ok:
        print("🎉 All systems ready!")
        print("1. Run: python install_godot_mcp_zip.py")
        print("2. Follow the Godot MCP setup guide")
    else:
        print("1. Restart your terminal/VS Code")
        print("2. Test commands:")
        print("   - node --version")
        print("   - npm --version")
        print("   - godot --version")
        print("3. If all working, run: python install_godot_mcp_zip.py")
    
    print("\n💡 TIPS:")
    print("- PATH changes require terminal restart to take effect")
    print("- You can now use 'godot' command from any directory")
    print("- The Godot MCP server will enable AI-assisted game development")
    
    return node_ok and npm_ok and godot_ok

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Ready for Godot MCP installation!")
    else:
        print("\n⚠️  Please restart terminal and try again")
