:allow_comments: False

.. _doc_contributing_workflow:

Contribution workflow
=====================

Godot is a large project, both in terms of the codebase and the workload for
contributors. The guides below serve as a helper when you get stuck dealing
with Git or GitHub, or if you are generally interested in how maintainers
approach the project.

.. toctree::
   :maxdepth: 1
   :name: toc-contributing-workflow

   first_steps
   bisecting_regressions
   bug_triage_guidelines
   pr_workflow
   pr_review_guidelines
   testing_pull_requests
