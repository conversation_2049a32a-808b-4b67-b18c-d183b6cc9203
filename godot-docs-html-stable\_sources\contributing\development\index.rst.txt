:allow_comments: False

.. _doc_contributing_to_the_engine:

Engine development
==================

The guides below explain how to contribute to the engine's codebase. You will learn
about best practices when working on solutions and features, the code style and the
internal structure of the codebase, how to set up your working environment, and more.

Be sure to also check the :ref:`workflow guidelines <doc_contributing_workflow>`,
especially if you're new to using Git or GitHub.

.. toctree::
   :maxdepth: 1
   :name: toc-contributing-to-the-engine

   best_practices_for_engine_contributors
   code_style_guidelines
   cpp_usage_guidelines
   handling_compatibility_breakages

Buildsystem and work environment
--------------------------------

.. toctree::
   :maxdepth: 1
   :name: toc-contributing-to-the-engine-environment

   configuring_an_ide/index
   compiling/index
   debugging/index

Engine architecture
-------------------

.. toctree::
   :maxdepth: 1
   :name: toc-contributing-to-the-engine-architecture

   core_and_modules/index
   file_formats/index
   editor/index
