.. _doc_ways_to_contribute:

How to contribute
=================

The Godot Engine is free and open-source. Like any community-driven project, we rely on volunteer contributions.
On this page we want to showcase the various ways you as users can participate - to help you find the right starting place with the skillset you have.
Because contrary to popular opinion, we need more than just programmers on the project!


Fundraising
-----------

- **Donate**

  We created the non-profit `Godot Foundation <https://godot.foundation/>`_ to be able to support the Godot Engine in both matters of finance and administration.
  In practice, this means the Foundation hires people to work part-time or full-time on the project.
  These jobs include engine development as well as related tasks like code reviews, production management, community & marketing efforts, and more.

  With as little as 5 EUR per month, you can help us keep going strong.
  Currently, we are intending to hire more core developers, as to cover more ground with full-time specialists that supplement and guide volunteer work.

  `Join the Development Fund <https://fund.godotengine.org>`_

- **Donation Drives**
  Think about your followers on social media, or other communities you are active in.
  Use that reach to remind your social environment that even small contributions can make a difference, especially when done by a great number of people at the same time.

  Are you a content creator? Consider adding a link to the `Godot Development Fund <https://fund.godotengine.org>`_ to your descriptions. 
  If you do live streams, perhaps think about organizing a stream with donation incentives.

.. - **Buy Official Merch**

- **Publish Godot Games.**
  You heard right, simply publishing a game #MadeWithGodot can positively impact the well-being of this project.
  Your personal success elevates the engine to a viable alternative for other developers, growing the community further.
  Additionally, it opens the doors for us to approach industry contacts about possible cooperations.


Technical contributions
-----------------------

- **Report bugs & other issues**
  As active users of the engine, you are better equipped to identify bugs and other issues than anyone else.
  To let us know about your findings, fill out this `bug report form <https://github.com/godotengine/godot/issues/new/choose>`_ on our GitHub.
  Make sure to include as much information as possible to ensure these issues can easily be reproduced by others.

  If you are interested in helping keep our bug tracker organized, you can even join the `bugsquad <https://chat.godotengine.org/channel/bugsquad>`_!

- **Test Development Versions**
  While it is recommended to use the stable releases for your projects, you can help us test dev releases, betas, and release candidates
  by opening a copy of your project in them and checking what problems this introduces or maybe even solves.
  Make sure to have a backup ready, since this can produce irreversible changes.

  Find recent `development versions <https://godotengine.org/download/preview/>`_ directly on our download page, or linked in their own blog posts.

- **Contribute Engine Code (mainly C++)**
  The engine development is mainly coordinated on our `Contributor RocketChat <https://chat.godotengine.org/>`_,
  so if you are serious about making PRs you should join us there!

  Read more about the **technical submission process**: :ref:`doc_first_steps`

  For each subject area of the engine, there is a corresponding team to coordinate the work.
  Join the linked chat to get more eyes on your related PR, learn about open todos, or partake in meetings.
  For some areas, specialists might even be encouraged to step up as maintainer!
  `List of teams <https://godotengine.org/teams/>`_

- **Review Code Contributions**
  All pull requests need to be thoroughly reviewed before they can be merged into the master branch.
  Help us get a headstart by participating in the code review process.

  To get started, chose any `open pull request <https://github.com/godotengine/godot/pulls>`_ and reference our **style guide**: :ref:`doc_pr_review_guidelines`

- **Write Plugins (GDScript, C#, & more)**
  Community addons are not directly included in the core engine download or repository, yet they provide essential quality of life upgrades for your fellow game developers.
  Upload your plugins to the `Godot Asset Library <https://godotengine.org/asset-library/asset>`_ to make them available to others.

  ..
    update to talk about Asset Store later
- **Demo projects (GDScript, C#, and making Assets)**
  We provide new users with `demo projects <https://github.com/godotengine/godot-demo-projects/>`_ so they can quickly test new features or get familiar with the engine in the first place.
  At industry events, we might even exhibit these demo projects to showcase what Godot can do!
  Help improve existing projects or supply your own to be added to the pool, and join the `demo channel <https://chat.godotengine.org/channel/demo-content>`_ in the Contributor RocketChat to talk about it.

- **Documentation**
  The documentation is one of the most essential parts of any tech project, yet the need to document new features and substantial changes often gets overlooked.
  Join the `documentation team <https://chat.godotengine.org/channel/documentation>`_ to improve the Godot Engine with your technical writing skills.

- **Translations (spoken languages other than English)**
  Are you interested in making the Godot Engine more accessible to non-English speakers?
  Contribute to our `community-translations <https://hosted.weblate.org/projects/godot-engine/godot/>`_.

Community support
-----------------

- **Call for Moderators**
  With a community of our size, we need people to step up as volunteer moderators in all kinds of places.
  These teams are organized by the Godot Foundation, but would not function without the dedication of active community members like you.

  Have a look around your favorite community platform and you might come across open application calls.

- **Answer tech-support questions**
  With many new people discovering the Godot Engine recently, the need for peer-to-peer tech-support has never been greater.
  See the `Godot website <https://godotengine.org/community>`_ for a list of official and user-supported Godot communities.

- **Create tutorials & more**
  How did you get started with the Godot Engine?
  Chances are you looked for learning materials outside of what the documentation provides.
  Without content creators covering the game development process, there would not be this big of a community today.
  Therefore it seemed only right to mention them in a page about important contributions to the project.

