#!/usr/bin/env python3
"""
Verify that Node.js and <PERSON><PERSON> are properly set up in environment PATH.
Run this after restarting your terminal/VS Code.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def test_command(name, command, timeout=10):
    """Test if a command works and return version info."""
    try:
        result = subprocess.run(command, capture_output=True, text=True, timeout=timeout)
        if result.returncode == 0:
            version = result.stdout.strip()
            return True, version
        else:
            return False, f"Command failed: {result.stderr.strip()}"
    except subprocess.TimeoutExpired:
        return True, "Timeout (likely working but slow to respond)"
    except FileNotFoundError:
        return False, "Command not found in PATH"
    except Exception as e:
        return False, f"Error: {e}"

def check_file_exists(name, file_path):
    """Check if a file exists and get its size."""
    if os.path.exists(file_path):
        size_mb = os.path.getsize(file_path) / (1024 * 1024)
        return True, f"{size_mb:.1f} MB"
    else:
        return False, "File not found"

def check_path_contains(path_to_check):
    """Check if a specific path is in the current PATH."""
    current_path = os.environ.get("PATH", "")
    path_parts = [p.strip() for p in current_path.split(';') if p.strip()]
    
    for part in path_parts:
        if path_to_check.lower() in part.lower():
            return True, part
    
    return False, "Not found in PATH"

def main():
    """Main verification function."""
    print("🔍 ENVIRONMENT SETUP VERIFICATION")
    print("=" * 60)
    print("Run this after restarting your terminal/VS Code")
    print()
    
    # Test commands
    print("🧪 TESTING COMMANDS:")
    print("-" * 30)
    
    commands_to_test = [
        ("Node.js", ["node", "--version"]),
        ("npm", ["npm", "--version"]),
        ("Godot (batch)", ["godot", "--version"]),
        ("Godot (direct)", ["Godot_v4.4.1-stable_win64.exe", "--version"])
    ]
    
    command_results = {}
    
    for name, cmd in commands_to_test:
        success, info = test_command(name, cmd)
        command_results[name] = success
        
        if success:
            print(f"✅ {name}: {info}")
        else:
            print(f"❌ {name}: {info}")
    
    # Check PATH contents
    print(f"\n📁 CHECKING PATH CONTENTS:")
    print("-" * 30)
    
    paths_to_check = [
        ("Node.js path", r"C:\Program Files\nodejs"),
        ("Desktop path", r"C:\Users\<USER>\OneDrive\Desktop")
    ]
    
    path_results = {}
    
    for name, path in paths_to_check:
        in_path, info = check_path_contains(path)
        path_results[name] = in_path
        
        if in_path:
            print(f"✅ {name}: Found in PATH")
            print(f"   {info}")
        else:
            print(f"❌ {name}: {info}")
    
    # Check file existence
    print(f"\n📄 CHECKING FILES:")
    print("-" * 30)
    
    files_to_check = [
        ("node.exe", r"C:\Program Files\nodejs\node.exe"),
        ("npm.cmd", r"C:\Program Files\nodejs\npm.cmd"),
        ("Godot 4.4.1", r"C:\Users\<USER>\OneDrive\Desktop\Godot_v4.4.1-stable_win64.exe"),
        ("godot.bat", r"C:\Users\<USER>\OneDrive\Desktop\godot.bat")
    ]
    
    file_results = {}
    
    for name, file_path in files_to_check:
        exists, info = check_file_exists(name, file_path)
        file_results[name] = exists
        
        if exists:
            print(f"✅ {name}: {info}")
        else:
            print(f"❌ {name}: {info}")
    
    # Check which commands work from PATH
    print(f"\n🔍 CHECKING COMMAND AVAILABILITY:")
    print("-" * 30)
    
    path_commands = ["node", "npm", "godot"]
    
    for cmd in path_commands:
        cmd_path = shutil.which(cmd)
        if cmd_path:
            print(f"✅ '{cmd}' found at: {cmd_path}")
        else:
            print(f"❌ '{cmd}' not found in PATH")
    
    # Overall assessment
    print(f"\n" + "=" * 60)
    print("📋 OVERALL ASSESSMENT")
    print("=" * 60)
    
    # Count successes
    total_commands = len(command_results)
    working_commands = sum(command_results.values())
    
    total_paths = len(path_results)
    working_paths = sum(path_results.values())
    
    total_files = len(file_results)
    existing_files = sum(file_results.values())
    
    print(f"\n📊 RESULTS:")
    print(f"   Commands working: {working_commands}/{total_commands}")
    print(f"   Paths in PATH: {working_paths}/{total_paths}")
    print(f"   Files found: {existing_files}/{total_files}")
    
    # Determine overall status
    if working_commands >= 3 and working_paths >= 1 and existing_files >= 3:
        print(f"\n🎉 SETUP STATUS: EXCELLENT!")
        print(f"✅ Environment is properly configured")
        print(f"✅ Ready for Godot MCP integration")
        
        print(f"\n🚀 NEXT STEPS:")
        print(f"1. ✅ All systems ready!")
        print(f"2. Run: python complete_godot_mcp_setup.py")
        print(f"3. Start using Augment Agent with Godot commands")
        
    elif working_commands >= 2:
        print(f"\n⚠️  SETUP STATUS: MOSTLY WORKING")
        print(f"✅ Core functionality available")
        print(f"⚠️  Some minor issues detected")
        
        print(f"\n🔧 RECOMMENDATIONS:")
        if not command_results.get("npm", False):
            print(f"- npm might need a terminal restart")
        if not command_results.get("Godot (batch)", False):
            print(f"- Try using full Godot path instead of 'godot' command")
        
        print(f"\n🚀 NEXT STEPS:")
        print(f"1. Try: python complete_godot_mcp_setup.py")
        print(f"2. Use full paths if commands don't work")
        
    else:
        print(f"\n❌ SETUP STATUS: NEEDS ATTENTION")
        print(f"❌ Multiple issues detected")
        
        print(f"\n🔧 TROUBLESHOOTING:")
        print(f"1. Restart your terminal/VS Code again")
        print(f"2. Check if paths were added correctly")
        print(f"3. Try running as Administrator")
        print(f"4. Re-run: python add_to_environment_path.py")
    
    # Usage examples
    print(f"\n💡 USAGE EXAMPLES:")
    print(f"   node --version          # Check Node.js")
    print(f"   npm --version           # Check npm")
    print(f"   godot --version         # Check Godot (via batch)")
    print(f"   Godot_v4.4.1-stable_win64.exe --version  # Direct Godot")
    
    return working_commands >= 3

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎉 Verification complete - environment is ready!")
    else:
        print(f"\n⚠️  Verification shows issues - see recommendations above")
